<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ title }}</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <!-- 地质风格头部 -->
    <header class="custom-header">
        <div class="logo">
            <div class="logo-icon">
                <i class="fas fa-mountain"></i>
            </div>
            <div class="logo-text">
                <h1>边坡监测系统</h1>
            </div>
        </div>
        <div class="header-controls">
            <!-- 系统状态已还原到这里 -->
            <span class="navbar-text" id="system-status">
                系统状态: <span class="badge">加载中...</span>
            </span>
            <a href="#" class="header-btn" data-bs-toggle="modal" data-bs-target="#settingsModal">
                <i class="fas fa-cog"></i> <span>系统设置</span>
            </a>
            <a href="#" class="header-btn" data-bs-toggle="modal" data-bs-target="#alertsModal">
                <i class="fas fa-history"></i> <span>告警记录</span>
            </a>
        </div>
    </header>

    <!-- 主内容区 -->
    <div class="container-fluid mt-4 main-container">
        <div class="row h-100">
            <!-- 左侧控制面板 -->
            <div class="col-md-3 left-panel-container">
                <!-- 检测设置卡片 -->
                <div class="geology-card mb-4" id="settings-card">
                    <div class="card-title">
                        <i class="fas fa-sliders-h"></i>
                        <span>检测设置</span>
                    </div>
                    <div class="card-body p-0">
                        <div class="setting-row">
                            <label for="modelSelect">检测模型</label>
                            <select class="form-select custom-form-element" id="modelSelect">
                                <option value="model1">边坡监测模型v1</option>
                                <option value="model2">边坡监测模型v2</option>
                            </select>
                        </div>
                        <div class="setting-row" id="yoloWorldClassContainer" style="display: none;">
                            <label for="yoloWorldClassSelect">识别类别</label>
                            <select class="form-select custom-form-element" id="yoloWorldClassSelect">
                                <option value="all">全部</option>
                                <option value="landslide">滑坡</option>
                                <option value="stone">落石</option>
                                <option value="road collapse">塌陷路面</option>
                                <option value="fallen tree">倒树</option>
                            </select>
                        </div>
                        <div class="setting-row">
                            <label for="intervalInput">检测时间间隔 (秒)</label>
                            <input type="number" class="form-control custom-form-element" id="intervalInput" value="{{ detection_interval }}" min="1" max="60">
                        </div>
                        <div class="setting-row-vertical">
                            <label>图像预处理模式</label>
                            <div class="form-check form-switch custom-switch">
                                <input class="form-check-input" type="checkbox" id="nightSwitch">
                                <label class="form-check-label" for="nightSwitch">增强模式1</label>
                            </div>
                            <div class="form-check form-switch custom-switch">
                                <input class="form-check-input" type="checkbox" id="rainSwitch">
                                <label class="form-check-label" for="rainSwitch">增强模式2</label>
                            </div>
                            <div class="form-check form-switch custom-switch">
                                <input class="form-check-input" type="checkbox" id="fogSwitch">
                                <label class="form-check-label" for="fogSwitch">增强模式3</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近告警卡片 -->
                <div class="geology-card" id="alerts-card">
                     <div class="card-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>最新告警</span>
                    </div>
                    <div class="card-body p-0" id="recent-alerts">
                        <p class="text-muted text-center py-4">暂无告警</p>
                    </div>
                </div>
            </div>

            <!-- 右侧监控视图区 (还原为你原来的布局) -->
            <div class="col-md-9">
                <div class="geology-card">
                    <div class="card-title d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-camera-retro"></i>
                            <span>监测区域</span>
                        </div>
                         <button class="btn btn-geology-primary" data-bs-toggle="modal" data-bs-target="#addSourceModal">
                            <i class="fas fa-plus-circle"></i> 数据源
                        </button>
                    </div>
                    <div class="card-body p-0">
                         <div class="row" id="video-container">
                            <!-- 视频流将在这里动态添加 (保持你原来的 2x2 网格布局) -->
                            <div class="col-12 text-center py-5 placeholder-text">
                                <i class="fas fa-video-slash"></i>
                                <p>暂无数据源，请点击右上角按钮添加</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- 添加视频源模态框 -->
    <div class="modal fade" id="addSourceModal" tabindex="-1" aria-labelledby="addSourceModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addSourceModalLabel">添加数据源</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="sourceTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="webcam-tab" data-bs-toggle="tab" data-bs-target="#webcam" type="button" role="tab" aria-controls="webcam" aria-selected="true">摄像头</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="video-tab" data-bs-toggle="tab" data-bs-target="#video" type="button" role="tab" aria-controls="video" aria-selected="false">视频文件</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="image-tab" data-bs-toggle="tab" data-bs-target="#image" type="button" role="tab" aria-controls="image" aria-selected="false">图像文件</button>
                        </li>
                    </ul>
                    <div class="tab-content mt-3" id="sourceTabContent">
                        <div class="tab-pane fade show active" id="webcam" role="tabpanel" aria-labelledby="webcam-tab">
                            <form id="webcam-form">
                                <div class="mb-3">
                                    <label for="webcamId" class="form-label">摄像头ID</label>
                                    <input type="number" class="form-control" id="webcamId" value="0" min="0">
                                </div>
                                <div class="mb-3">
                                    <label for="webcamName" class="form-label">名称</label>
                                    <input type="text" class="form-control" id="webcamName" placeholder="摄像头">
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="video" role="tabpanel" aria-labelledby="video-tab">
                            <form id="video-form">
                                <div class="mb-3">
                                    <label for="videoFile" class="form-label">视频文件</label>
                                    <input class="form-control" type="file" id="videoFile" accept="video/*">
                                </div>
                                <div class="mb-3">
                                    <label for="videoName" class="form-label">名称</label>
                                    <input type="text" class="form-control" id="videoName" placeholder="视频">
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="videoLoop">
                                    <label class="form-check-label" for="videoLoop">循环播放</label>
                                </div>
                            </form>
                        </div>
                        <div class="tab-pane fade" id="image" role="tabpanel" aria-labelledby="image-tab">
                            <form id="image-form">
                                <div class="mb-3">
                                    <label for="imageFile" class="form-label">图像文件</label>
                                    <input class="form-control" type="file" id="imageFile" accept="image/*">
                                </div>
                                <div class="mb-3">
                                    <label for="imageName" class="form-label">名称</label>
                                    <input type="text" class="form-control" id="imageName" placeholder="图像">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="addSourceBtn">添加</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设置模态框 -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel">系统设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6>邮件告警设置</h6>

                    <div class="mb-3">
                        <label for="emailAddresses" class="form-label">接收告警的邮箱地址</label>
                        <input type="text" class="form-control" id="emailAddresses" placeholder="多个邮箱用逗号分隔">
                    </div>
                    <div class="mb-3">
                        <label for="smtpServer" class="form-label">SMTP服务器</label>
                        <input type="text" class="form-control" id="smtpServer" placeholder="例如: smtp.163.com">
                    </div>
                    <div class="row mb-3">
                        <div class="col-6">
                            <label for="smtpPort" class="form-label">SMTP端口</label>
                            <input type="number" class="form-control" id="smtpPort" placeholder="例如: 465" value="465">
                        </div>
                        <div class="col-6">
                            <label for="smtpSsl" class="form-label">SSL连接</label>
                            <select class="form-select" id="smtpSsl">
                                <option value="1" selected>是</option>
                                <option value="0">否</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="senderEmail" class="form-label">发件人邮箱</label>
                        <input type="email" class="form-control" id="senderEmail" placeholder="发送邮件的邮箱地址">
                    </div>
                    <div class="mb-3">
                        <label for="senderPassword" class="form-label">发件人密码/授权码</label>
                        <input type="password" class="form-control" id="senderPassword" placeholder="邮箱密码或授权码">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="saveSettingsBtn">保存</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 告警历史模态框 -->
    <div class="modal fade" id="alertsModal" tabindex="-1" aria-labelledby="alertsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="alertsModalLabel">告警记录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="alerts-container">
                        <p class="text-muted text-center">正在加载告警记录...</p>
                    </div>
                    <!-- 新增分页控件容器 -->
                    <nav>
                        <ul class="pagination justify-content-center mt-3" id="alerts-pagination"></ul>
                    </nav>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered"> <!-- modal-dialog-centered 使其垂直居中 -->
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmDeleteModalLabel">
                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                        <span>确认操作</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body fs-6 text-center py-4">
                    <p class="mb-1">您确定要删除以下数据源吗？</p>
                    <p><strong><span id="confirmDeleteSourceName"></span></strong></p>
                    <small class="text-muted">此操作不可恢复。</small>
                </div>
                <div class="modal-footer">
                    <!-- “确认删除”按钮使用自定义的红色样式 -->
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>