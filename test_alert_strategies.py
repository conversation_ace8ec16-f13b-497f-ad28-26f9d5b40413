#!/usr/bin/env python3
"""
测试邮件告警冷却策略的脚本
用于验证三种不同策略的行为
"""

import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.app.utils.alert import <PERSON><PERSON>Manager, EmailAlertProvider

def test_email_provider_strategies():
    """测试邮件提供者的不同冷却策略"""
    print("=== 测试邮件提供者冷却策略 ===\n")
    
    # 模拟邮件配置（不会真正发送邮件）
    test_config = {
        'smtp_server': 'smtp.test.com',
        'smtp_port': 465,
        'sender_email': '<EMAIL>',
        'sender_password': 'test_password',
        'recipient_emails': ['<EMAIL>'],
        'use_ssl': True
    }
    
    # 测试告警数据
    test_alerts = [
        {
            'source_id': 'camera_1',
            'type': 'detection',
            'message': '检测到滑坡',
            'level': 'warning',
            'timestamp': time.time(),
            'datetime': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        {
            'source_id': 'camera_2',
            'type': 'detection',
            'message': '检测到落石',
            'level': 'warning',
            'timestamp': time.time(),
            'datetime': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        {
            'source_id': 'camera_3',
            'type': 'detection',
            'message': '检测到塌陷',
            'level': 'warning',
            'timestamp': time.time(),
            'datetime': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        {
            'source_id': 'camera_1',  # 重复源
            'type': 'detection',
            'message': '再次检测到滑坡',
            'level': 'warning',
            'timestamp': time.time(),
            'datetime': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    ]
    
    strategies = ['none', 'global', 'per_source']
    
    for strategy in strategies:
        print(f"\n--- 测试策略: {strategy} ---")
        
        # 创建邮件提供者
        provider = EmailAlertProvider(
            cooldown_strategy=strategy,
            cooldown_seconds=3,  # 使用较短的冷却时间便于测试
            **test_config
        )
        
        # 重写send方法以避免真正发送邮件
        def mock_send(self, alert):
            if not self._can_send_alert(alert):
                print(f"❌ 源 {alert['source_id']} 的告警被冷却机制阻止")
                return
            
            self._update_sent_time(alert)
            print(f"✅ 源 {alert['source_id']} 的告警发送成功: {alert['message']}")
        
        # 绑定模拟方法
        provider.send = lambda alert: mock_send(provider, alert)
        
        print(f"冷却策略: {strategy}, 冷却时间: {provider.cooldown_seconds}秒")
        
        # 发送测试告警
        for i, alert in enumerate(test_alerts):
            print(f"\n第{i+1}次发送 - 源: {alert['source_id']}")
            provider.send(alert)
            
            # 显示冷却信息
            cooldown_info = provider.get_cooldown_info()
            if strategy == 'global' and cooldown_info['global_remaining'] > 0:
                print(f"  全局剩余冷却: {cooldown_info['global_remaining']:.1f}秒")
            elif strategy == 'per_source':
                for source_id, remaining in cooldown_info['source_remaining'].items():
                    if remaining > 0:
                        print(f"  源 {source_id} 剩余冷却: {remaining:.1f}秒")
            
            # 短暂延迟
            time.sleep(0.5)
        
        print(f"\n策略 {strategy} 测试完成")
        print("-" * 50)

def test_alert_manager():
    """测试告警管理器"""
    print("\n=== 测试告警管理器 ===\n")
    
    # 创建告警管理器
    alert_manager = AlertManager(cooldown_seconds=2)
    
    # 测试告警数据
    test_data = [
        ('camera_1', 'detection', '检测到异常1'),
        ('camera_2', 'detection', '检测到异常2'),
        ('camera_1', 'detection', '再次检测到异常1'),  # 应该被冷却
        ('camera_3', 'detection', '检测到异常3'),
    ]
    
    print("测试告警管理器冷却机制:")
    print(f"冷却时间: {alert_manager.cooldown_seconds}秒\n")
    
    for i, (source_id, alert_type, message) in enumerate(test_data):
        print(f"第{i+1}次告警 - 源: {source_id}")
        
        # 检查冷却
        key = f"{source_id}_{alert_type}"
        current_time = time.time()
        
        if key in alert_manager.alert_history:
            last_time = alert_manager.alert_history[key]
            remaining = alert_manager.cooldown_seconds - (current_time - last_time)
            if remaining > 0:
                print(f"❌ 告警被冷却机制阻止 (剩余: {remaining:.1f}秒)")
                continue
        
        # 更新告警历史
        alert_manager.alert_history[key] = current_time
        print(f"✅ 告警通过: {message}")
        
        time.sleep(0.5)
    
    # 显示状态
    status = alert_manager.get_alert_status()
    print(f"\n告警管理器状态:")
    print(f"- 冷却时间: {status['cooldown_seconds']}秒")
    print(f"- 队列大小: {status['queue_size']}")
    print(f"- 运行状态: {status['is_running']}")

def demonstrate_multi_source_scenario():
    """演示多源同时检测的场景"""
    print("\n=== 多源同时检测场景演示 ===\n")
    
    print("场景：四个摄像头同时检测到异常")
    print("时间间隔：几乎同时（0.1秒间隔）\n")
    
    # 模拟配置
    test_config = {
        'smtp_server': 'smtp.test.com',
        'smtp_port': 465,
        'sender_email': '<EMAIL>',
        'sender_password': 'test_password',
        'recipient_emails': ['<EMAIL>'],
        'use_ssl': True
    }
    
    # 四个源的告警
    alerts = []
    for i in range(1, 5):
        alerts.append({
            'source_id': f'camera_{i}',
            'type': 'detection',
            'message': f'摄像头{i}检测到滑坡',
            'level': 'warning',
            'timestamp': time.time(),
            'datetime': time.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    strategies_to_test = [
        ('global', 30, '原有全局冷却策略'),
        ('per_source', 30, '方案1：按源冷却策略'),
        ('per_source', 5, '方案2：缩短冷却时间'),
        ('none', 0, '方案3：无冷却限制')
    ]
    
    for strategy, cooldown_seconds, description in strategies_to_test:
        print(f"\n--- {description} ---")
        print(f"策略: {strategy}, 冷却时间: {cooldown_seconds}秒")
        
        # 创建邮件提供者
        provider = EmailAlertProvider(
            cooldown_strategy=strategy,
            cooldown_seconds=cooldown_seconds,
            **test_config
        )
        
        # 模拟发送方法
        def mock_send(self, alert):
            if not self._can_send_alert(alert):
                print(f"❌ {alert['source_id']} 邮件被阻止")
                return
            
            self._update_sent_time(alert)
            print(f"✅ {alert['source_id']} 邮件发送成功")
        
        provider.send = lambda alert: mock_send(provider, alert)
        
        # 模拟同时检测
        print("模拟四个源几乎同时检测到异常:")
        for alert in alerts:
            provider.send(alert)
            time.sleep(0.1)  # 模拟很短的时间间隔
        
        print(f"结果：{description}")
        print("-" * 60)

if __name__ == "__main__":
    print("邮件告警冷却策略测试")
    print("=" * 60)
    
    try:
        # 运行测试
        test_email_provider_strategies()
        test_alert_manager()
        demonstrate_multi_source_scenario()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("\n推荐配置：")
        print("- 生产环境：per_source策略，30秒冷却")
        print("- 测试环境：per_source策略，5秒冷却")
        print("- 高安全要求：none策略，无冷却")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
