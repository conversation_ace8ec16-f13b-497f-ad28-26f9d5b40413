import cv2
import numpy as np
import time
from datetime import datetime

def draw_detections(frame, detections, add_timestamp=True):
    """
    在图像帧上绘制检测结果
    Args:
        frame: 原始图像帧
        detections: 检测结果列表，每个元素是包含 box, confidence, class_name 的字典
        add_timestamp: 是否添加时间戳
    Returns:
        绘制了检测结果的图像帧
    """
    # 创建副本以避免修改原始帧
    result_frame = frame.copy()
    # 绘制每个检测结果
    for detection in detections:
        # 获取检测框
        x1, y1, x2, y2 = detection['box']
        confidence = detection['confidence']
        class_name = detection['class_name']
        # 确保坐标是整数
        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
        # 绘制边界框 - 红色
        cv2.rectangle(result_frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
        # 绘制标签背景 - 半透明
        label = f"{class_name}: {confidence:.2f}"
        text_size, _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)
        cv2.rectangle(result_frame, (x1, y1 - 20), (x1 + text_size[0], y1), (0, 0, 255), -1)
        # 绘制标签文本 - 白色
        cv2.putText(result_frame, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

    # 添加时间戳和检测信息
    if add_timestamp:
        now = datetime.now()
        timestamp_str = now.strftime("%Y-%m-%d %H:%M:%S")
        detection_info = f"Detections: {len(detections)}"
        # 添加时间戳 - 左上角
        cv2.putText(result_frame, timestamp_str, (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        # 添加检测信息 - 左上角第二行
        cv2.putText(result_frame, detection_info, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    return result_frame

def draw_status_info(frame, source_info, detection_age=None):
    """
    在图像帧上绘制状态信息
    Args:
        frame: 原始图像帧
        source_info: 视频源信息
        detection_age: 检测结果的年龄（秒）
    Returns:
        添加了状态信息的图像帧
    """
    # 创建副本以避免修改原始帧
    result_frame = frame.copy()
    # 左下角绘制源信息
    name = source_info.get('name', 'Unknown')
    cv2.putText(result_frame, f"Source: {name}", (10, frame.shape[0] - 40), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
    # 如果有检测结果年龄信息，显示它
    if detection_age is not None:
        cv2.putText(result_frame, f"Detection: {detection_age:.1f}s ago", 
                   (10, frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
    # 添加当前时间
    now = datetime.now().strftime("%H:%M:%S")
    cv2.putText(result_frame, now, (frame.shape[1] - 100, frame.shape[0] - 20), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)
    
    return result_frame 