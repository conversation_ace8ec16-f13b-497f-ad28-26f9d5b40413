/* 1. 定义颜色和字体变量 (地质风格核心) */
:root {
    --geology-bg: #f8f5f2;
    --geology-card: #ffffff;
    --rock-primary: #6d4c41;
    --rock-secondary: #8d6e63;
    --mineral-green: #4caf50;
    --mineral-red: #d9534f; /* 告警红色 */
    --text-dark: #333333;
    --text-gray: #757575;
    --border-light: #e0e0e0;
    --shadow-light: rgba(0, 0, 0, 0.08);
}

/* 2. 全局和Body样式 */
html, body { height: 100%; }
* { box-sizing: border-box; }
body {
    background-color: var(--geology-bg);
    color: var(--text-dark);
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path d="M0 0 L100 0 L100 100 L0 100 Z" fill="none" stroke="%23e0d7d1" stroke-width="0.5"/><path d="M20 20 Q40 10, 60 20 T100 30" stroke="%23e0d7d1" stroke-width="0.3" fill="none"/><path d="M10 50 Q30 40, 50 50 T90 60" stroke="%23e0d7d1" stroke-width="0.3" fill="none"/><path d="M30 70 Q50 60, 70 70 T95 80" stroke="%23e0d7d1" stroke-width="0.3" fill="none"/></svg>');
    background-size: 200px 200px;
    display: flex;
    flex-direction: column;
}

/* 3. 头部样式 */
.custom-header {
    background-color: var(--geology-card);
    padding: 15px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px var(--shadow-light);
    position: sticky; top: 0; z-index: 1021;
}
.logo { display: flex; align-items: center; gap: 15px; }
.logo-icon {
    width: 45px; height: 45px;
    background: linear-gradient(135deg, var(--rock-primary), var(--rock-secondary));
    border-radius: 50%;
    display: flex; align-items: center; justify-content: center;
    color: white; font-size: 22px;
}
.logo-text h1 { font-size: 1.8rem; color: var(--rock-primary); margin: 0; }
.logo-text p { font-size: 0.9rem; color: var(--text-gray); margin: 0; }
.header-controls { display: flex; align-items: center; gap: 15px; }
.header-btn {
    padding: 8px 18px;
    background: var(--geology-card);
    border: 1px solid var(--border-light);
    border-radius: 30px;
    color: var(--rock-primary);
    text-decoration: none;
    display: flex; align-items: center; gap: 8px;
    font-weight: 500;
    transition: all 0.3s;
}
.header-btn:hover {
    background: var(--rock-primary);
    color: white;
    box-shadow: 0 4px 12px rgba(109, 76, 65, 0.2);
}
/* 还原系统状态样式 */
.navbar-text { color: var(--rock-primary); }
.navbar-text .badge { padding: .5em .8em; font-size: 0.9em; }
.navbar-text .bg-success { background-color: var(--mineral-green) !important; }
.navbar-text .bg-danger { background-color: var(--mineral-red) !important; }

/* 4. 主内容区和卡片布局 */
.main-container { flex-grow: 1; overflow: hidden; }
.geology-card {
    background: var(--geology-card);
    border-radius: 16px;
    padding: 25px 30px;
    box-shadow: 0 8px 30px var(--shadow-light);
    border: 1px solid var(--border-light);
    display: flex; /* 让卡片内部也用flex布局 */
    flex-direction: column;
}
.geology-card .card-body {
    flex-grow: 1; /* 让卡片主体部分填充空间 */
}
.card-title {
    font-size: 1.5rem; color: var(--rock-primary); font-weight: 600;
    display: flex; align-items: center; gap: 12px;
    padding-bottom: 15px; margin-bottom: 20px;
    border-bottom: 1px solid var(--border-light);
}

/* 5. 表单和设置项样式 */
.setting-row, .setting-row-vertical {
    display: flex; justify-content: space-between; align-items: center;
    padding: 12px 5px; border-bottom: 1px solid #f2f2f2;
}
.setting-row-vertical { flex-direction: column; align-items: flex-start; gap: 10px; }
.geology-card .card-body > div:last-child { border-bottom: none; }
.setting-row > label, .setting-row-vertical > label {
    font-weight: 600; color: var(--rock-secondary); font-size: 0.95rem;
}
.custom-form-element {
    border: 1px solid var(--border-light) !important; border-radius: 8px !important;
    background-color: #fafafa !important; color: var(--text-dark) !important; max-width: 180px;
}
.custom-form-element:focus {
    box-shadow: 0 0 0 3px rgba(109, 76, 65, 0.15) !important; border-color: var(--rock-secondary) !important;
}
.custom-switch .form-check-input { background-color: #e0d7d1; border-color: #d0c6bf; }
.custom-switch .form-check-input:checked { background-color: var(--rock-primary); border-color: var(--rock-primary); }
.custom-switch .form-check-label { color: var(--text-dark); }

/* 6. 监控视图区样式 (还原你的核心布局控制) */
.placeholder-text {
    display: flex; flex-direction: column; align-items: center; justify-content: center;
    gap: 15px; color: var(--text-gray); font-size: 1.2rem; min-height: 300px;
}
.placeholder-text i { font-size: 3rem; color: #d7ccc8; }
.btn-geology-primary {
    background-color: var(--rock-primary); color: white; border: none;
    padding: 8px 16px; border-radius: 8px; transition: all 0.3s;
}
.btn-geology-primary:hover { background-color: var(--rock-secondary); color: white; }

/* 还原视频卡片高度控制，以实现四图同屏 */
.video-card {
    position: relative;
}
.video-card .card {
    display: flex; flex-direction: column; /* 保持卡片内部flex布局 */
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: 0 4px 15px var(--shadow-light);
    overflow: hidden;
    height: 100%;
}
.video-card .card-img-top {
    width: 100%; height: 100%;
    object-fit: contain;
    max-height: 32vh; /* 这是你保持四图同屏的关键！ */
}
.video-card .source-controls {
    position: absolute; top: 10px; right: 10px; z-index: 10;
}
.video-card .source-controls .btn {
    background-color: rgba(0, 0, 0, 0.5); color: white; border: none;
    width: 28px; height: 28px; border-radius: 50%; padding: 0;
    line-height: 28px; transition: all 0.3s ease; opacity: 0.7;
}
.video-card .source-controls .btn:hover {
    opacity: 1; background-color: var(--mineral-red); transform: scale(1.1);
}
.stream-loading-overlay, .stream-error-overlay {
    position: absolute; top: 0; left: 0; width: 100%; height: 100%;
    background: rgba(0,0,0,0.7); display: flex; flex-direction: column;
    justify-content: center; align-items: center; z-index: 5; color: white;
}

/* 7. 告警信息样式 */
#recent-alerts .alert-item {
    border-left: 4px solid var(--mineral-red); padding: 10px 15px;
    margin-bottom: 12px; background-color: #fcf8f7; border-radius: 4px;
}
#recent-alerts .alert-item .alert-time { font-size: 0.8rem; color: var(--text-gray); }
#recent-alerts .alert-item .alert-message { font-weight: bold; margin: 4px 0; color: var(--rock-primary); }
#recent-alerts .alert-item .alert-source { font-size: 0.9rem; color: var(--rock-secondary); }


/************************************************************/
/* --- 【新增】第8部分：左侧面板布局控制 --- */
/************************************************************/
.left-panel-container {
    display: flex;
    flex-direction: column;
    /* 视口高度减去header和上下padding，你可以微调 120px 这个值 */
    height: calc(100vh - 120px);
}
#settings-card {
    flex-shrink: 0; /* 设置卡片不收缩，保持其内容决定的高度 */
}
#alerts-card {
    flex-grow: 1; /* 告警卡片填充所有剩余垂直空间 */
    min-height: 0; /* 关键属性，允许flex item在空间不足时收缩 */
}
#recent-alerts {
    height: 100%; /* 让告警列表内容区占满其父容器（card-body）的高度 */
    overflow-y: auto; /* 当内容超出时，自动显示垂直滚动条 */
    padding-right: 10px; /* 为滚动条留出一些空间，避免内容紧贴 */
}

/************************************************************/
/* --- 【新增】第9部分：模态框(Modal)风格重写 --- */
/************************************************************/
.modal-content {
    background-color: var(--geology-bg);
    border: 1px solid var(--border-light);
    border-radius: 16px;
    box-shadow: 0 8px 30px var(--shadow-light);
}
.modal-header {
    background-color: var(--geology-card);
    color: var(--rock-primary);
    border-bottom: 1px solid var(--border-light);
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
}
.modal-header .btn-close {
    filter: invert(30%) sepia(20%) saturate(1000%) hue-rotate(345deg) brightness(90%) contrast(90%);
}
.modal-body {
    padding: 25px 30px;
}
.modal-footer {
    background-color: var(--geology-card);
    border-top: 1px solid #f0f0f0;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
}
.modal-footer .btn-primary {
    background-color: var(--rock-primary);
    border-color: var(--rock-primary);
}
.modal-footer .btn-primary:hover {
    background-color: var(--rock-secondary);
    border-color: var(--rock-secondary);
}
.modal-footer .btn-secondary {
    background-color: #f0f0f0;
    border-color: #e0e0e0;
    color: var(--text-gray);
}
.modal-footer .btn-secondary:hover {
    background-color: #e0e0e0;
}
/* 模态框内的Tabs样式 */
.modal-body .nav-tabs {
    border-bottom: 1px solid var(--border-light);
}
.modal-body .nav-link {
    color: var(--text-gray);
    border: none;
    border-bottom: 3px solid transparent;
}
.modal-body .nav-link.active,
.modal-body .nav-link:hover {
    color: var(--rock-primary);
    border-color: var(--rock-primary);
    background-color: transparent;
}
/* 模态框内的表单元素 */
.modal-body .form-control,
.modal-body .form-select {
    border-color: var(--border-light);
}
.modal-body .form-control:focus,
.modal-body .form-select:focus {
    box-shadow: 0 0 0 3px rgba(109, 76, 65, 0.15) !important;
    border-color: var(--rock-secondary) !important;
}
/* 告警历史里的图片 */
.alert-history-item img {
    border-radius: 8px;
    border: 1px solid var(--border-light);
}

/************************************************************/
/* --- 【新增】第10部分：告警历史(Modal)内容美化 --- */
/************************************************************/

/* 告警历史条目整体样式 */
.alert-history-item {
    background-color: #fdfbf9; /* 一个非常淡的、温暖的米白色 */
    border: 1px solid var(--border-light);
    border-left: 5px solid var(--mineral-red); /* 左侧的红色强调线 */
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

/* 添加一个漂亮的鼠标悬停效果 */
.alert-history-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.07);
}

/* 右侧信息区域 */
.alert-history-item .alert-info {
    padding-left: 10px;
}

/* 告警标题样式 (H6) */
.alert-info .alert-title {
    font-size: 1.25rem;
    color: var(--rock-primary);
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

/* 标题前面的感叹号图标 */
.alert-info .alert-title i {
    margin-right: 10px;
    color: var(--mineral-red); /* 图标使用告警红色 */
    font-size: 1.1rem;
}

/* 每一行信息 (时间和来源) */
.alert-info .info-line {
    margin-bottom: 10px;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}

/* 信息的标签部分，例如 "时间:" */
.alert-info .info-label {
    font-weight: 600;
    color: var(--rock-secondary); /* 使用次要的棕色 */
    margin-right: 8px;
    flex-shrink: 0; /* 防止标签被压缩 */
}

/* 信息的数值部分，例如 "2025-07-05 ..." */
.alert-info .info-value {
    color: var(--text-dark);
    word-break: break-all; /* 如果来源名称过长，则换行 */
}

/* START: 新增自定义确认弹窗样式 */
#confirmDeleteModal .modal-footer .btn-danger {
    background-color: var(--mineral-red); /* 使用您定义的告警红色 */
    border-color: var(--mineral-red);
    color: white;
}

#confirmDeleteModal .modal-footer .btn-danger:hover {
    background-color: #c9302c; /* 一个稍暗的红色 */
    border-color: #ac2925;
}

#confirmDeleteModal .modal-body strong {
    color: var(--rock-primary); /* 数据源名称使用主要岩石色，更醒目 */
    font-size: 1.1rem;
}