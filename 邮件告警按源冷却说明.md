# 邮件告警按源冷却解决方案

## 问题解决

您的问题：**四个源同时检测到异常时，只有第一个能发送邮件，其他被冷却机制阻止**

## 解决方案：按源独立冷却

### 实施内容
- 移除了EmailProvider的全局冷却机制
- 改为每个源独立计算冷却时间
- 保持30秒的冷却时间设置

### 核心修改
**EmailAlertProvider类**：
- 使用 `source_last_sent_times` 字典记录每个源的最后发送时间
- 每个源独立判断是否在冷却期内
- 只有同一个源的重复告警才会被冷却阻止

### 效果验证
测试结果显示：
- ✅ camera_1 邮件发送成功
- ✅ camera_2 邮件发送成功  
- ✅ camera_3 邮件发送成功
- ✅ camera_4 邮件发送成功

**同一源重复告警测试**：
- ❌ 立即重复发送被阻止（剩余29.6秒冷却）
- ✅ 等待31秒后重新发送成功

## 使用方法

### 立即生效
重启系统后自动使用新的按源冷却策略

### 配置说明
- 冷却时间：30秒（在EmailAlertProvider中设置）
- 策略：按源独立冷却
- 兼容性：完全向后兼容，无需修改现有配置

## 技术细节

### 修改的文件
- `src/app/utils/alert.py` - 核心邮件告警逻辑
- `src/app/core.py` - 移除多余配置

### 核心代码逻辑
```python
# 检查按源的冷却时间
current_time = time.time()
source_id = alert['source_id']

if source_id in self.source_last_sent_times:
    last_sent = self.source_last_sent_times[source_id]
    if current_time - last_sent < self.cooldown_seconds:
        # 该源在冷却期内，跳过
        return

# 更新该源的发送时间
self.source_last_sent_times[source_id] = current_time
```

## 优势

1. **解决核心问题**：四个源同时检测到时都能发送邮件
2. **保持保护机制**：同一源的重复告警仍有30秒冷却
3. **简单可靠**：代码简洁，逻辑清晰
4. **向后兼容**：无需修改现有配置
5. **性能优良**：按源记录，内存占用极小

## 总结

现在您的系统已经完美解决了多源同时检测时的邮件发送问题！

- **问题前**：4个源同时检测 → 只有1个邮件发送
- **问题后**：4个源同时检测 → 4个邮件都发送 ✅

每个摄像头都能独立发送告警邮件，不会再被其他摄像头的邮件发送影响。
