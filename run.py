import os
import logging
import sys
import webbrowser
import threading
import time
import json
from src.app import create_app

# 将 src 目录加入 sys.path，确保可以正确导入模块
src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), 'src'))
if src_path not in sys.path:
    sys.path.insert(0, src_path)

def open_browser():
    """在新线程中打开浏览器，避免阻塞主线程"""
    time.sleep(1.5)  # 等待服务器启动
    url = "http://localhost:5000"
    try:
        webbrowser.open(url)
        print(f"已在浏览器中打开页面: {url}")
    except Exception as e:
        print(f"自动打开浏览器失败: {e}")

def ensure_data_files():
    """确保 data 目录和必须的 json 文件存在，兼容 exe 路径"""
    from src.app.core import get_data_dir
    data_dir = get_data_dir()
    os.makedirs(data_dir, exist_ok=True)
    # 检查 settings.json
    settings_path = os.path.join(data_dir, 'settings.json')
    if not os.path.exists(settings_path):
        with open(settings_path, 'w', encoding='utf-8') as f:
            json.dump({}, f)
    # 检查 sources.json
    sources_path = os.path.join(data_dir, 'sources.json')
    if not os.path.exists(sources_path):
        with open(sources_path, 'w', encoding='utf-8') as f:
            json.dump({}, f)

def main():
    ensure_data_files()  # 启动前确保目录和文件存在

    # 清除现有的日志处理器，避免重复
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 配置日志格式和级别（只在这里配置一次）
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        force=True  # 强制重新配置，避免重复
    )

    # 降低不必要模块的日志级别
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('ultralytics').setLevel(logging.WARNING)

    # 确保所有logger都不会传播到父logger（避免重复）
    for logger_name in ['src.app.core', 'src.app.models.detector', 'src.app.utils.alert']:
        logger = logging.getLogger(logger_name)
        logger.propagate = True  # 保持传播，但确保没有重复处理器

    # 创建 Flask 应用实例
    app = create_app()

    # 自动设置环境变量，开发时为 development，生产为 production
    debug_mode = False  # 开发时可改为 True
    os.environ['FLASK_ENV'] = 'development' if debug_mode else 'production'

    # 需要监控的目录（模板、样式、脚本、Python源码）
    extra_dirs = [
        'src/app/templates',
        'src/app/static/css',
        'src/app/static/js',
        'src/app'  # 监控 Python 源码，便于热重载
    ]
    extra_files = []
    for extra_dir in extra_dirs:
        for dir_name, dirs, files in os.walk(extra_dir):
            for filename in files:
                # 只监控常用前端和 Python 文件
                if filename.endswith(('.py', '.html', '.css', '.js')):
                    filepath = os.path.join(dir_name, filename)
                    if os.path.isfile(filepath):
                        extra_files.append(filepath)

    # 仅在主进程中自动打开浏览器
    if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
        threading.Thread(target=open_browser, daemon=True).start()

    # 启动 Flask 应用
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=debug_mode,         # 开发时建议 True，生产为 False
        use_reloader=False,       # 关闭热重载避免重复日志
        extra_files=extra_files,  # 监控指定文件变化
        reloader_type='stat'      # 使用 stat 方式监控文件变化
    )

if __name__ == '__main__':
    main()