import logging
import requests
import json
import threading
import time
import queue
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from datetime import datetime
import os

logger = logging.getLogger(__name__)

class AlertManager:
    """告警管理器，负责发送和管理告警"""
    def __init__(self, cooldown_seconds=30):
        self.lock = threading.RLock()
        self.alert_providers = {}
        self.alert_history = {}  # 记录告警
        self.cooldown_seconds = cooldown_seconds  # 同一来源的告警冷却时间
        self.alert_queue = queue.Queue()
        self.is_running = False
        self.worker_thread = None
    
    def register_provider(self, provider_name, provider):
        """
        注册告警提供者
        Args:
            provider_name: 提供者名称
            provider: AlertProvider实例
        """
        with self.lock:
            self.alert_providers[provider_name] = provider
            logger.info(f"注册告警提供者: {provider_name}")
    
    def unregister_provider(self, provider_name):
        """移除告警提供者"""
        with self.lock:
            if provider_name in self.alert_providers:
                del self.alert_providers[provider_name]
                logger.info(f"移除告警提供者: {provider_name}")
    
    def start(self):
        """启动告警处理线程"""
        with self.lock:
            if not self.is_running:
                self.is_running = True
                self.worker_thread = threading.Thread(target=self._process_alerts, daemon=True)
                self.worker_thread.start()
                logger.info("告警管理器启动")
    
    def stop(self):
        """停止告警处理线程"""
        with self.lock:
            if self.is_running:
                self.is_running = False
                if self.worker_thread and self.worker_thread.is_alive():
                    self.worker_thread.join(timeout=2.0)
                logger.info("告警管理器停止")
    
    def send_alert(self, source_id, alert_type, message, image=None, level="warning"):
        """
        发送告警
        Args:
            source_id: 告警源ID
            alert_type: 告警类型
            message: 告警消息
            image: 告警图像(可选)
            level: 告警级别(info/warning/error)
        """
        # 检查告警冷却期
        key = f"{source_id}_{alert_type}"
        current_time = time.time()
        
        with self.lock:
            if key in self.alert_history:
                last_time = self.alert_history[key]
                if current_time - last_time < self.cooldown_seconds:
                    logger.debug(f"告警 {key} 在冷却期内，跳过")
                    return
            
            # 更新告警历史
            self.alert_history[key] = current_time
        # 创建告警对象
        alert = {
            'source_id': source_id,
            'type': alert_type,
            'message': message,
            'image': image,
            'level': level,
            'timestamp': current_time,
            'datetime': datetime.fromtimestamp(current_time).strftime('%Y-%m-%d %H:%M:%S')
        }
        # 加入队列
        self.alert_queue.put(alert)
        logger.info(f"添加告警到队列: {alert_type} - {message}")
    
    def _process_alerts(self):
        """告警处理线程"""
        while self.is_running:
            try:
                # 从队列中获取告警
                try:
                    alert = self.alert_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 遍历所有告警提供者发送告警
                with self.lock:
                    providers = list(self.alert_providers.values())
                
                for provider in providers:
                    try:
                        provider.send(alert)
                    except Exception as e:
                        logger.error(f"发送告警失败: {e}")
                
                self.alert_queue.task_done()
            
            except Exception as e:
                logger.error(f"处理告警时出错: {e}")
                time.sleep(1)  # 发生错误时短暂暂停

class AlertProvider:
    """告警提供者基类"""
    def send(self, alert):
        """
        发送告警(由子类实现)
        Args:
            alert: 告警对象
        """
        raise NotImplementedError("子类必须实现此方法")

class SMSAlertProvider(AlertProvider):
    """短信告警提供者"""
    
    def __init__(self, api_key, template_id, phone_numbers=None):
        self.api_key = api_key
        self.template_id = template_id
        self.phone_numbers = phone_numbers or []
        self.api_url = "https://api.example.com/sms"  # 实际使用时需替换为真实的API地址
    
    def add_phone_number(self, phone):
        """添加手机号码"""
        if phone not in self.phone_numbers:
            self.phone_numbers.append(phone)
    
    def remove_phone_number(self, phone):
        """移除手机号码"""
        if phone in self.phone_numbers:
            self.phone_numbers.remove(phone)
    
    def send(self, alert):
        """发送短信告警"""
        if not self.phone_numbers:
            logger.warning("没有配置手机号码，无法发送短信告警")
            return
        
        if not self.api_key:
            logger.warning("没有配置API密钥，无法发送短信告警")
            return
        
        try:
            # 准备请求数据
            alert_time = alert.get('datetime', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            data = {
                'apikey': self.api_key,
                'template_id': self.template_id,
                'mobile': ','.join(self.phone_numbers),
                'params': {
                    'source': alert['source_id'],
                    'type': alert['type'],
                    'message': alert['message'],
                    'time': alert_time,
                    'level': alert['level']
                }
            }
            # 发送请求
            response = requests.post(
                self.api_url,
                data=json.dumps(data),
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            # 检查响应
            if response.status_code == 200:
                logger.info(f"短信告警发送成功: {alert['message']}")
            else:
                logger.error(f"短信告警发送失败: {response.status_code} - {response.text}")
        
        except Exception as e:
            logger.error(f"发送短信告警时出错: {e}")

class WebhookAlertProvider(AlertProvider):
    """Webhook告警提供者"""
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url
    
    def send(self, alert):
        """发送Webhook告警"""
        if not self.webhook_url:
            logger.warning("没有配置Webhook URL，无法发送告警")
            return
        
        try:
            # 准备请求数据
            data = {
                'source_id': alert['source_id'],
                'type': alert['type'],
                'message': alert['message'],
                'level': alert['level'],
                'timestamp': alert['timestamp'],
                'datetime': alert['datetime']
            }
            # 发送请求
            response = requests.post(
                self.webhook_url,
                data=json.dumps(data),
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            # 检查响应
            if response.status_code == 200:
                logger.info(f"Webhook告警发送成功: {alert['message']}")
            else:
                logger.error(f"Webhook告警发送失败: {response.status_code} - {response.text}")
        
        except Exception as e:
            logger.error(f"发送Webhook告警时出错: {e}")

class LogAlertProvider(AlertProvider):
    """日志告警提供者(将告警记录到日志中)"""
    def send(self, alert):
        """将告警记录到日志"""
        log_message = f"[告警] {alert['datetime']} - 来源: {alert['source_id']}, 类型: {alert['type']}, 级别: {alert['level']}, 消息: {alert['message']}"
        
        if alert['level'] == 'error':
            logger.error(log_message)
        elif alert['level'] == 'warning':
            logger.warning(log_message)
        else:
            logger.info(log_message) 

class EmailAlertProvider(AlertProvider):
    """邮件告警提供者"""
    def __init__(self, smtp_server, smtp_port, sender_email, sender_password, recipient_emails=None, use_ssl=True):
        """
        初始化邮件告警提供者
        Args:
            smtp_server: SMTP服务器地址
            smtp_port: SMTP服务器端口
            sender_email: 发件人邮箱
            sender_password: 发件人密码或授权码
            recipient_emails: 收件人邮箱列表
            use_ssl: 是否使用SSL连接
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.sender_email = sender_email
        self.sender_password = sender_password
        self.recipient_emails = recipient_emails or []
        self.use_ssl = use_ssl
        self.cooldown_seconds = 30  # 按源冷却时间，30秒

        # 按源的冷却时间记录
        self.source_last_sent_times = {}  # 每个源独立的冷却时间
    
    def add_recipient_email(self, email):
        """添加收件人邮箱"""
        if email not in self.recipient_emails:
            self.recipient_emails.append(email)
    
    def remove_recipient_email(self, email):
        """移除收件人邮箱"""
        if email in self.recipient_emails:
            self.recipient_emails.remove(email)
    
    def send(self, alert):
        """发送邮件告警"""
        if not self.recipient_emails:
            logger.warning("没有配置收件人邮箱，无法发送邮件告警")
            return

        # 检查按源的冷却时间
        current_time = time.time()
        source_id = alert['source_id']

        if source_id in self.source_last_sent_times:
            last_sent = self.source_last_sent_times[source_id]
            if current_time - last_sent < self.cooldown_seconds:
                logger.debug(f"源 {source_id} 的邮件告警在冷却期内，跳过 (剩余: {self.cooldown_seconds - (current_time - last_sent):.1f}秒)")
                return

        # 更新该源的发送时间
        self.source_last_sent_times[source_id] = current_time

        try:
            logger.info(f"准备发送邮件告警: 收件人={self.recipient_emails}, SMTP={self.smtp_server}:{self.smtp_port}, SSL={self.use_ssl}")
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = ', '.join(self.recipient_emails)
            msg['Subject'] = f"边坡检测系统告警: {alert['type']}"

            # 邮件正文
            body = f"""
            <html>
            <body>
                <h2>边坡检测系统告警</h2>
                <p><strong>时间：</strong>{alert['datetime']}</p>
                <p><strong>来源：</strong>{alert['source_id']}</p>
                <p><strong>类型：</strong>{alert['type']}</p>
                <p><strong>级别：</strong>{alert['level']}</p>
                <p><strong>消息：</strong>{alert['message']}</p>
                <p>请及时查看系统检测详情。</p>
            </body>
            </html>
            """
            msg.attach(MIMEText(body, 'html'))

            # 如果有图像附件，添加到邮件中
            if alert.get('image') and os.path.exists(alert['image']):
                logger.info(f"邮件告警添加图片附件: {alert['image']}")
                with open(alert['image'], 'rb') as f:
                    img_data = f.read()
                    image = MIMEImage(img_data)
                    image_filename = os.path.basename(alert['image'])
                    image.add_header('Content-Disposition', 'attachment', filename=image_filename)
                    msg.attach(image)

            # 连接SMTP服务器并发送
            if self.use_ssl:
                logger.info("使用SMTP_SSL连接服务器")
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                logger.info("使用SMTP连接服务器并启动STARTTLS")
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()

            logger.info(f"登录邮箱: {self.sender_email}")
            server.login(self.sender_email, self.sender_password)
            server.send_message(msg)
            server.quit()

            logger.info(f"邮件告警发送成功: {alert['message']}")

        except Exception as e:
            logger.error(f"发送邮件告警时出错: {e}", exc_info=True)