import logging
import requests
import json
import threading
import time
import queue
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from datetime import datetime
import os

logger = logging.getLogger(__name__)

class AlertManager:
    """告警管理器，负责发送和管理告警"""
    def __init__(self, cooldown_seconds=30):
        self.lock = threading.RLock()
        self.alert_providers = {}
        self.alert_history = {}  # 记录告警
        self.cooldown_seconds = cooldown_seconds  # 同一来源的告警冷却时间
        self.alert_queue = queue.Queue()
        self.is_running = False
        self.worker_thread = None
    
    def register_provider(self, provider_name, provider):
        """
        注册告警提供者
        Args:
            provider_name: 提供者名称
            provider: AlertProvider实例
        """
        with self.lock:
            self.alert_providers[provider_name] = provider
            logger.info(f"注册告警提供者: {provider_name}")
    
    def unregister_provider(self, provider_name):
        """移除告警提供者"""
        with self.lock:
            if provider_name in self.alert_providers:
                del self.alert_providers[provider_name]
                logger.info(f"移除告警提供者: {provider_name}")
    
    def start(self):
        """启动告警处理线程"""
        with self.lock:
            if not self.is_running:
                self.is_running = True
                self.worker_thread = threading.Thread(target=self._process_alerts, daemon=True)
                self.worker_thread.start()
                logger.info("告警管理器启动")
    
    def stop(self):
        """停止告警处理线程"""
        with self.lock:
            if self.is_running:
                self.is_running = False
                if self.worker_thread and self.worker_thread.is_alive():
                    self.worker_thread.join(timeout=2.0)
                logger.info("告警管理器停止")
    
    def send_alert(self, source_id, alert_type, message, image=None, level="warning"):
        """
        发送告警
        Args:
            source_id: 告警源ID
            alert_type: 告警类型
            message: 告警消息
            image: 告警图像(可选)
            level: 告警级别(info/warning/error)
        """
        # 检查告警冷却期
        key = f"{source_id}_{alert_type}"
        current_time = time.time()
        
        with self.lock:
            if key in self.alert_history:
                last_time = self.alert_history[key]
                if current_time - last_time < self.cooldown_seconds:
                    logger.debug(f"告警 {key} 在冷却期内，跳过")
                    return
            
            # 更新告警历史
            self.alert_history[key] = current_time
        # 创建告警对象
        alert = {
            'source_id': source_id,
            'type': alert_type,
            'message': message,
            'image': image,
            'level': level,
            'timestamp': current_time,
            'datetime': datetime.fromtimestamp(current_time).strftime('%Y-%m-%d %H:%M:%S')
        }
        # 加入队列
        self.alert_queue.put(alert)
        logger.info(f"添加告警到队列: {alert_type} - {message}")
    
    def _process_alerts(self):
        """告警处理线程"""
        while self.is_running:
            try:
                # 从队列中获取告警
                try:
                    alert = self.alert_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 遍历所有告警提供者发送告警
                with self.lock:
                    providers = list(self.alert_providers.values())
                
                for provider in providers:
                    try:
                        provider.send(alert)
                    except Exception as e:
                        logger.error(f"发送告警失败: {e}")
                
                self.alert_queue.task_done()
            
            except Exception as e:
                logger.error(f"处理告警时出错: {e}")
                time.sleep(1)  # 发生错误时短暂暂停

    def set_cooldown_seconds(self, cooldown_seconds):
        """
        设置告警冷却时间
        Args:
            cooldown_seconds: 冷却时间（秒）
        """
        self.cooldown_seconds = cooldown_seconds
        logger.info(f"告警管理器冷却时间已更新为: {cooldown_seconds}秒")

    def get_alert_status(self):
        """
        获取告警状态信息
        Returns:
            dict: 告警状态信息
        """
        with self.lock:
            current_time = time.time()
            status = {
                'cooldown_seconds': self.cooldown_seconds,
                'queue_size': self.alert_queue.qsize(),
                'is_running': self.is_running,
                'providers': list(self.alert_providers.keys()),
                'alert_history': {}
            }

            # 计算每个源的剩余冷却时间
            for key, last_time in self.alert_history.items():
                remaining = max(0, self.cooldown_seconds - (current_time - last_time))
                status['alert_history'][key] = {
                    'last_time': last_time,
                    'remaining_cooldown': remaining
                }

            return status

class AlertProvider:
    """告警提供者基类"""
    def send(self, alert):
        """
        发送告警(由子类实现)
        Args:
            alert: 告警对象
        """
        raise NotImplementedError("子类必须实现此方法")

class SMSAlertProvider(AlertProvider):
    """短信告警提供者"""
    
    def __init__(self, api_key, template_id, phone_numbers=None):
        self.api_key = api_key
        self.template_id = template_id
        self.phone_numbers = phone_numbers or []
        self.api_url = "https://api.example.com/sms"  # 实际使用时需替换为真实的API地址
    
    def add_phone_number(self, phone):
        """添加手机号码"""
        if phone not in self.phone_numbers:
            self.phone_numbers.append(phone)
    
    def remove_phone_number(self, phone):
        """移除手机号码"""
        if phone in self.phone_numbers:
            self.phone_numbers.remove(phone)
    
    def send(self, alert):
        """发送短信告警"""
        if not self.phone_numbers:
            logger.warning("没有配置手机号码，无法发送短信告警")
            return
        
        if not self.api_key:
            logger.warning("没有配置API密钥，无法发送短信告警")
            return
        
        try:
            # 准备请求数据
            alert_time = alert.get('datetime', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            data = {
                'apikey': self.api_key,
                'template_id': self.template_id,
                'mobile': ','.join(self.phone_numbers),
                'params': {
                    'source': alert['source_id'],
                    'type': alert['type'],
                    'message': alert['message'],
                    'time': alert_time,
                    'level': alert['level']
                }
            }
            # 发送请求
            response = requests.post(
                self.api_url,
                data=json.dumps(data),
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            # 检查响应
            if response.status_code == 200:
                logger.info(f"短信告警发送成功: {alert['message']}")
            else:
                logger.error(f"短信告警发送失败: {response.status_code} - {response.text}")
        
        except Exception as e:
            logger.error(f"发送短信告警时出错: {e}")

class WebhookAlertProvider(AlertProvider):
    """Webhook告警提供者"""
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url
    
    def send(self, alert):
        """发送Webhook告警"""
        if not self.webhook_url:
            logger.warning("没有配置Webhook URL，无法发送告警")
            return
        
        try:
            # 准备请求数据
            data = {
                'source_id': alert['source_id'],
                'type': alert['type'],
                'message': alert['message'],
                'level': alert['level'],
                'timestamp': alert['timestamp'],
                'datetime': alert['datetime']
            }
            # 发送请求
            response = requests.post(
                self.webhook_url,
                data=json.dumps(data),
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            # 检查响应
            if response.status_code == 200:
                logger.info(f"Webhook告警发送成功: {alert['message']}")
            else:
                logger.error(f"Webhook告警发送失败: {response.status_code} - {response.text}")
        
        except Exception as e:
            logger.error(f"发送Webhook告警时出错: {e}")

class LogAlertProvider(AlertProvider):
    """日志告警提供者(将告警记录到日志中)"""
    def send(self, alert):
        """将告警记录到日志"""
        log_message = f"[告警] {alert['datetime']} - 来源: {alert['source_id']}, 类型: {alert['type']}, 级别: {alert['level']}, 消息: {alert['message']}"
        
        if alert['level'] == 'error':
            logger.error(log_message)
        elif alert['level'] == 'warning':
            logger.warning(log_message)
        else:
            logger.info(log_message) 

class EmailAlertProvider(AlertProvider):
    """邮件告警提供者"""
    def __init__(self, smtp_server, smtp_port, sender_email, sender_password, recipient_emails=None, use_ssl=True, cooldown_strategy='per_source', cooldown_seconds=30):
        """
        初始化邮件告警提供者
        Args:
            smtp_server: SMTP服务器地址
            smtp_port: SMTP服务器端口
            sender_email: 发件人邮箱
            sender_password: 发件人密码或授权码
            recipient_emails: 收件人邮箱列表
            use_ssl: 是否使用SSL连接
            cooldown_strategy: 冷却策略 ('none', 'global', 'per_source')
                - 'none': 无冷却限制，每次检测到都发送
                - 'global': 全局冷却，所有源共享冷却时间
                - 'per_source': 按源冷却，每个源独立冷却（推荐）
            cooldown_seconds: 冷却时间（秒）
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.sender_email = sender_email
        self.sender_password = sender_password
        self.recipient_emails = recipient_emails or []
        self.use_ssl = use_ssl
        self.cooldown_strategy = cooldown_strategy
        self.cooldown_seconds = cooldown_seconds

        # 不同策略的冷却时间记录
        self.last_sent_time = 0  # 全局冷却时间
        self.source_last_sent_times = {}  # 按源的冷却时间
    
    def add_recipient_email(self, email):
        """添加收件人邮箱"""
        if email not in self.recipient_emails:
            self.recipient_emails.append(email)
    
    def remove_recipient_email(self, email):
        """移除收件人邮箱"""
        if email in self.recipient_emails:
            self.recipient_emails.remove(email)
    
    def send(self, alert):
        """发送邮件告警"""
        if not self.recipient_emails:
            logger.warning("没有配置收件人邮箱，无法发送邮件告警")
            return

        # 根据冷却策略检查是否可以发送
        if not self._can_send_alert(alert):
            return

        # 更新发送时间记录
        self._update_sent_time(alert)

        try:
            logger.info(f"准备发送邮件告警: 收件人={self.recipient_emails}, SMTP={self.smtp_server}:{self.smtp_port}, SSL={self.use_ssl}")
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = ', '.join(self.recipient_emails)
            msg['Subject'] = f"边坡检测系统告警: {alert['type']}"

            # 邮件正文
            body = f"""
            <html>
            <body>
                <h2>边坡检测系统告警</h2>
                <p><strong>时间：</strong>{alert['datetime']}</p>
                <p><strong>来源：</strong>{alert['source_id']}</p>
                <p><strong>类型：</strong>{alert['type']}</p>
                <p><strong>级别：</strong>{alert['level']}</p>
                <p><strong>消息：</strong>{alert['message']}</p>
                <p>请及时查看系统检测详情。</p>
            </body>
            </html>
            """
            msg.attach(MIMEText(body, 'html'))

            # 如果有图像附件，添加到邮件中
            if alert.get('image') and os.path.exists(alert['image']):
                logger.info(f"邮件告警添加图片附件: {alert['image']}")
                with open(alert['image'], 'rb') as f:
                    img_data = f.read()
                    image = MIMEImage(img_data)
                    image_filename = os.path.basename(alert['image'])
                    image.add_header('Content-Disposition', 'attachment', filename=image_filename)
                    msg.attach(image)

            # 连接SMTP服务器并发送
            if self.use_ssl:
                logger.info("使用SMTP_SSL连接服务器")
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            else:
                logger.info("使用SMTP连接服务器并启动STARTTLS")
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()

            logger.info(f"登录邮箱: {self.sender_email}")
            server.login(self.sender_email, self.sender_password)
            server.send_message(msg)
            server.quit()

            logger.info(f"邮件告警发送成功: {alert['message']}")

        except Exception as e:
            logger.error(f"发送邮件告警时出错: {e}", exc_info=True)

    def _can_send_alert(self, alert):
        """
        根据冷却策略检查是否可以发送告警
        Args:
            alert: 告警对象
        Returns:
            bool: 是否可以发送
        """
        current_time = time.time()
        source_id = alert['source_id']

        if self.cooldown_strategy == 'none':
            # 无冷却限制
            return True
        elif self.cooldown_strategy == 'global':
            # 全局冷却策略
            if current_time - self.last_sent_time < self.cooldown_seconds:
                logger.debug(f"邮件告警在全局冷却期内，跳过 (剩余: {self.cooldown_seconds - (current_time - self.last_sent_time):.1f}秒)")
                return False
            return True
        elif self.cooldown_strategy == 'per_source':
            # 按源冷却策略
            if source_id in self.source_last_sent_times:
                last_sent = self.source_last_sent_times[source_id]
                if current_time - last_sent < self.cooldown_seconds:
                    logger.debug(f"源 {source_id} 的邮件告警在冷却期内，跳过 (剩余: {self.cooldown_seconds - (current_time - last_sent):.1f}秒)")
                    return False
            return True
        else:
            logger.warning(f"未知的冷却策略: {self.cooldown_strategy}，使用默认per_source策略")
            return self._can_send_alert_per_source(alert, current_time)

    def _update_sent_time(self, alert):
        """
        更新发送时间记录
        Args:
            alert: 告警对象
        """
        current_time = time.time()
        source_id = alert['source_id']

        if self.cooldown_strategy == 'global':
            self.last_sent_time = current_time
        elif self.cooldown_strategy == 'per_source':
            self.source_last_sent_times[source_id] = current_time
        # 'none'策略不需要记录时间

    def set_cooldown_strategy(self, strategy, cooldown_seconds=None):
        """
        设置冷却策略
        Args:
            strategy: 冷却策略 ('none', 'global', 'per_source')
            cooldown_seconds: 冷却时间（秒），如果为None则保持当前设置
        """
        if strategy in ['none', 'global', 'per_source']:
            self.cooldown_strategy = strategy
            if cooldown_seconds is not None:
                self.cooldown_seconds = cooldown_seconds
            logger.info(f"邮件告警冷却策略已更新: {strategy}, 冷却时间: {self.cooldown_seconds}秒")
        else:
            logger.error(f"无效的冷却策略: {strategy}")

    def get_cooldown_info(self):
        """
        获取当前冷却策略信息
        Returns:
            dict: 冷却策略信息
        """
        current_time = time.time()
        info = {
            'strategy': self.cooldown_strategy,
            'cooldown_seconds': self.cooldown_seconds,
            'global_remaining': max(0, self.cooldown_seconds - (current_time - self.last_sent_time)) if self.cooldown_strategy == 'global' else 0,
            'source_remaining': {}
        }

        if self.cooldown_strategy == 'per_source':
            for source_id, last_sent in self.source_last_sent_times.items():
                remaining = max(0, self.cooldown_seconds - (current_time - last_sent))
                info['source_remaining'][source_id] = remaining

        return info