// 全局变量
let activeSources = {}; // 活动的视频源
let recentAlerts = []; // 最近的告警
let settingsBackup = {}; // 备份当前设置

// START: 新增自定义确认弹窗函数
/**
 * 显示自定义的确认删除模态框
 * @param {string} sourceName - 要显示在弹窗中的数据源名称
 * @param {function} callback - 用户点击“确认删除”后要执行的回调函数
 */
function showConfirmDeleteModal(sourceName, callback) {
    // 获取模态框实例
    const confirmModal = new bootstrap.Modal(document.getElementById('confirmDeleteModal'));
    // 设置弹窗内容
    $('#confirmDeleteSourceName').text(sourceName);

    // 为“确认删除”按钮绑定一次性点击事件
    $('#confirmDeleteBtn').off('click').one('click', function() {
        if (typeof callback === 'function') {
            callback(); // 执行回调
        }
        confirmModal.hide(); // 关闭模态框
    });
    // 显示模态框
    confirmModal.show();
}

// 页面加载完成后执行
$(document).ready(function() {
    registerEventHandlers();
    loadInitialData();
    setupPeriodicTasks();
});

// 注册所有事件处理程序
function registerEventHandlers() {
    $('#intervalInput').on('change', updateDetectionInterval);
    $('#modelSelect').on('change', updateModel);
    $('#nightSwitch, #rainSwitch, #fogSwitch').on('change', savePreprocessingSettings);
    $('#addSourceBtn').on('click', addSource);

    // 设置模态框相关
    let settingsSaved = false;
    $('#settingsModal').on('show.bs.modal', function() {
        loadSettings();
        setTimeout(backupSettings, 300);
    });
    $('#settingsModal').on('hide.bs.modal', function() {
        if (!settingsSaved) {
            restoreSettings();
        }
        settingsSaved = false;
    });
    $('#saveSettingsBtn').on('click', function() {
        settingsSaved = true;
        saveSettings();
    });

    $('#alertsModal').on('show.bs.modal', loadAlertHistory);

    $('#modelSelect').change(function() {
        const modelId = $(this).val();
        $('#yoloWorldClassContainer').toggle(modelId === 'model2');
    });

    $('#yoloWorldClassSelect').change(updateYoloWorldClass);
}

// 加载初始数据
function loadInitialData() {
    loadSources();
    loadRecentAlerts();
    loadSettings();
    updateSystemStatus();
}

// 设置周期性任务
function setupPeriodicTasks() {
    setInterval(loadRecentAlerts, 5000);
    setInterval(updateSystemStatus, 10000);
}

// 更新检测间隔
function updateDetectionInterval() {
    const interval = parseInt($('#intervalInput').val());
    $.ajax({
        url: '/api/detection_interval',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ interval: interval }),
        success: function(response) {
            showToast('检测间隔已更新');
        },
        error: function(xhr) {
            showToast('更新检测间隔失败', 'error');
            console.error('更新检测间隔失败:', xhr.responseText);
        }
    });
}

// 更新模型
function updateModel() {
    const model = $('#modelSelect').val();
    $.ajax({
        url: '/api/model',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ model: model }),
        success: function(response) {
            showToast(`已切换到 ${getModelName(model)}`);
            if (model === 'model2') {
                updateYoloWorldClass();
            }
        },
        error: function(xhr) {
            showToast('更新模型失败', 'error');
            console.error('更新模型失败:', xhr.responseText);
        }
    });
}

// 获取模型可读名称
function getModelName(modelValue) {
    return $('#modelSelect').find('option:selected').text() || modelValue;
}

function savePreprocessingSettings() {
    const settings = {
        nightMode: $('#nightSwitch').prop('checked'),
        rainMode: $('#rainSwitch').prop('checked'),
        fogMode: $('#fogSwitch').prop('checked')
    };
    $.ajax({
        url: '/api/settings',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(settings),
        success: function(response) {
            console.log('Preprocessing settings updated.');
        },
        error: function(xhr) {
            showToast('更新预处理设置失败', 'error');
            console.error('更新预处理设置失败:', xhr.responseText);
        }
    });
}


// 添加数据源（主函数）
function addSource() {
    const activeTabId = $('#sourceTab .nav-link.active').attr('id');
    const actions = {
        'webcam-tab': addWebcamSource,
        'video-tab': addVideoSource,
        'image-tab': addImageSource,
    };
    if (actions[activeTabId]) {
        actions[activeTabId]();
    }
}

// 添加各种源的子函数
function addWebcamSource() {
    const cameraId = $('#webcamId').val();
    const name = $('#webcamName').val() || `摄像头_${cameraId}`;
    if (!cameraId || isNaN(cameraId)) {
        showToast('请输入有效的摄像头编号', 'warning');
        return;
    }
    $.ajax({
        url: '/api/sources',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ type: 'webcam', camera_id: cameraId, name: name }),
        success: handleAddSourceSuccess,
        error: handleAddSourceError
    });
}

function addVideoSource() {
    const videoFile = $('#videoFile')[0].files[0];
    if (!videoFile) { return showToast('请选择视频文件', 'warning'); }
    const name = $('#videoName').val() || videoFile.name;
    const loop = $('#videoLoop').prop('checked');
    const formData = new FormData();
    formData.append('video', videoFile);
    formData.append('type', 'video');
    formData.append('name', name);
    formData.append('loop', loop);
    $.ajax({
        url: '/api/sources',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: handleAddSourceSuccess,
        error: handleAddSourceError
    });
}

function addImageSource() {
    const imageFile = $('#imageFile')[0].files[0];
    if (!imageFile) { return showToast('请选择图像文件', 'warning'); }
    const name = $('#imageName').val() || imageFile.name;
    const formData = new FormData();
    formData.append('image', imageFile);
    formData.append('type', 'image');
    formData.append('name', name);
    $.ajax({
        url: '/api/sources',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: handleAddSourceSuccess,
        error: handleAddSourceError
    });
}

function handleAddSourceSuccess(response) {
    $('#addSourceModal').modal('hide');
    showToast('数据源已添加');
    loadSources();
}

function handleAddSourceError(xhr) {
    showToast('添加数据源失败', 'error');
    console.error('添加数据源失败:', xhr.responseText);
}


// 设置相关的函数
function backupSettings() {
    settingsBackup = {
        emailAddresses: $('#emailAddresses').val(),
        smtpServer: $('#smtpServer').val(),
        smtpPort: $('#smtpPort').val(),
        smtpSsl: $('#smtpSsl').val(),
        senderEmail: $('#senderEmail').val(),
        senderPassword: $('#senderPassword').val()
    };
}

function restoreSettings() {
    Object.keys(settingsBackup).forEach(key => {
        $(`#${key}`).val(settingsBackup[key]);
    });
}

function saveSettings() {
    const settings = {
        emailSettings: {
            emailAddresses: $('#emailAddresses').val(),
            smtpServer: $('#smtpServer').val(),
            smtpPort: parseInt($('#smtpPort').val()),
            smtpSsl: $('#smtpSsl').val() === '1',
            senderEmail: $('#senderEmail').val(),
            senderPassword: $('#senderPassword').val()
        }
    };
    $.ajax({
        url: '/api/settings',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(settings),
        success: function(response) {
            $('#settingsModal').modal('hide');
            showToast('设置已保存', 'success');
            loadSettings(); // 4. 保存设置后自动刷新界面
        },
        error: function(xhr) {
            showToast('保存设置失败: ' + xhr.responseText, 'error');
            console.error('保存设置失败:', xhr.responseText);
        }
    });
}

function loadSettings() {
    $.ajax({
        url: '/api/settings',
        type: 'GET',
        success: function(response) {
            if (response.success && response.settings) {
                if (response.settings.emailSettings) {
                    $('#emailAddresses').val(response.settings.emailSettings.emailAddresses || '');
                    $('#smtpServer').val(response.settings.emailSettings.smtpServer || '');
                    $('#smtpPort').val(response.settings.emailSettings.smtpPort || 465);
                    $('#smtpSsl').val(response.settings.emailSettings.smtpSsl ? '1' : '0');
                    $('#senderEmail').val(response.settings.emailSettings.senderEmail || '');
                    $('#senderPassword').val(response.settings.emailSettings.senderPassword || '');
                }
                $('#nightSwitch').prop('checked', response.settings.nightMode);
                $('#rainSwitch').prop('checked', response.settings.rainMode);
                $('#fogSwitch').prop('checked', response.settings.fogMode);
            }
        },
        error: function(xhr) {
            console.error('加载设置失败:', xhr.responseText);
        }
    });
}

// 加载和更新UI的函数
function loadSources() {
    $.ajax({
        url: '/api/sources',
        type: 'GET',
        success: function(response) {
            updateSourcesUI(response.sources);
        }
    });
}

function updateSourcesUI(sources) {
    const container = $('#video-container');
    container.empty();

    if (!sources || sources.length === 0) {
        container.html(`
            <div class="col-12 text-center py-5 placeholder-text">
                <i class="fas fa-video-slash"></i>
                <p>暂无数据源，请点击右上角按钮添加</p>
            </div>`);
        return;
    }

    activeSources = {};
    sources.forEach(function(source) {
        const sourceId = source[0];
        const sourceName = source[1];
        activeSources[sourceId] = sourceName;

        const videoCard = `
            <div class="col-md-6 video-card mb-4" data-source-id="${sourceId}">
                <div class="card h-100">
                    <div class="source-controls">
                        <button class="btn btn-sm remove-source" data-source-id="${sourceId}" title="移除">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="position-relative flex-grow-1 d-flex align-items-center justify-content-center" style="background-color: #f0f0f0;">
                        <img src="/stream/${encodeURIComponent(sourceId)}" class="card-img-top" alt="${sourceName}"
                             onerror="handleStreamError(this, '${sourceId}')"
                             onload="handleStreamLoaded(this, '${sourceId}')">
                        <div class="stream-loading-overlay">
                             <div class="spinner-border text-light" role="status"></div>
                        </div>
                    </div>
                    <div class="card-body py-2">
                        <h6 class="card-title mb-0">${sourceName}</h6>
                    </div>
                </div>
            </div>
        `;
        container.append(videoCard);
    });

    $('.remove-source').on('click', function() {
        removeSource($(this).data('source-id'));
    });
}

function handleStreamLoaded(img, sourceId) {
    $(img).closest('.position-relative').find('.stream-loading-overlay').fadeOut();
}

function handleStreamError(img, sourceId) {
    const container = $(img).closest('.position-relative');
    container.find('.stream-loading-overlay').remove();

    const errorHtml = `
        <div class="stream-error-overlay">
            <div class="text-danger" style="font-size: 2rem;"><i class="fas fa-exclamation-triangle"></i></div>
            <div class="mt-2 text-light">加载失败</div>
        </div>`;

    if (!container.find('.stream-error-overlay').length) {
         container.append(errorHtml);
    }

    setTimeout(() => {
        $(img).attr('src', `/stream/${encodeURIComponent(sourceId)}?t=${new Date().getTime()}`);
    }, 30000);
}

function removeSource(sourceId) {
    const sourceName = activeSources[sourceId] || '此数据源';
    // 调用新的自定义确认弹窗
    showConfirmDeleteModal(sourceName, function() {
        // --- 这是用户点击“确认删除”后才会执行的代码 ---
        // 1. 立即从界面上移除卡片，提供即时反馈
        $(`.video-card[data-source-id="${sourceId}"]`).fadeOut(300, function() {
            $(this).remove();
            if ($('.video-card').length === 0) {
                updateSourcesUI([]); // 如果都删完了，显示占位符
            }
        });
        // 2. 从前端活动源列表中删除
        delete activeSources[sourceId];
        // 3. 向后端发送删除请求
        $.ajax({
            url: `/api/sources/${encodeURIComponent(sourceId)}`,
            type: 'DELETE',
            success: function(response) {
                showToast(`数据源 "${sourceName}" 已成功移除`);
            },
            error: function(xhr) {
                // 即便后端删除失败，卡片也已从UI移除，给用户一个提示即可
                if (xhr.status !== 404) {
                    showToast(`移除 "${sourceName}" 时发生错误`, 'error');
                }
            }
        });
    });
}

// 告警相关函数
function loadRecentAlerts() {
    $.ajax({
        url: '/api/alerts',
        type: 'GET',
        success: function(response) {
            updateRecentAlertsUI(response.alerts.slice(0, 5));
        },
        error: function(xhr) {
            console.error('加载告警失败:', xhr.responseText);
        }
    });
}

function updateRecentAlertsUI(alerts) {
    const container = $('#recent-alerts');
    container.empty();

    if (alerts.length === 0) {
        container.html('<p class="text-muted text-center py-4">暂无告警</p>');
        return;
    }

    alerts.forEach(function(alert) {
        const sourceName = activeSources[alert.source_id] || alert.source_id;
        const alertItem = `
            <div class="alert-item">
                <div class="alert-time">${alert.datetime}</div>
                <div class="alert-message">检测到异常</div>
                <div class="alert-source">来源: ${sourceName}</div>
            </div>
        `;
        container.append(alertItem);
    });
}

function loadAlertHistory(page = 1) {
    const container = $('#alerts-container');
    container.html('<div class="text-center p-5"><div class="spinner-border text-secondary" role="status"></div></div>');

    $.ajax({
        url: `/api/alerts?page=${page}`,
        type: 'GET',
        success: function(response) {
            updateAlertHistoryUI(response.alerts);
            // 可在此处更新分页控件
            // updatePagination(response.page, response.total_pages);
        },
        error: function(xhr) {
            container.html('<p class="text-danger text-center">加载告警记录失败</p>');
            showToast('加载告警记录失败: ' + xhr.responseText, 'error');
        }
    });
}

function updateAlertHistoryUI(alerts) {
    const container = $('#alerts-container');
    container.empty();

    if (alerts.length === 0) {
        container.html('<p class="text-muted text-center">暂无告警记录</p>');
        return;
    }

    alerts.forEach(function(alert) {
        const sourceName = activeSources[alert.source_id] || alert.source_id;

        const alertItem = `
            <div class="alert-history-item">
                <div class="row g-4 align-items-center">
                    <div class="col-md-5">
                        <img src="${alert.image_url}" class="img-fluid" alt="告警图像">
                    </div>
                    <div class="col-md-7">
                        <div class="alert-info">
                            <h6 class="alert-title">
                                <i class="fas fa-exclamation-triangle"></i> 检测到异常
                            </h6>
                            <div class="info-line">
                                <span class="info-label">时间:</span>
                                <span class="info-value">${alert.datetime}</span>
                            </div>
                            <div class="info-line">
                                <span class="info-label">来源:</span>
                                <span class="info-value">${sourceName}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.append(alertItem);
    });
}

// 新增分页控件渲染函数
function updatePagination(currentPage, totalPages) {
    const pagination = $('#alerts-pagination');
    pagination.empty();
    if (totalPages <= 1) return;

    // 上一页
    pagination.append(`
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a>
        </li>
    `);

    // 页码按钮（只显示最多5页，更多可自行扩展）
    let start = Math.max(1, currentPage - 2);
    let end = Math.min(totalPages, currentPage + 2);
    for (let i = start; i <= end; i++) {
        pagination.append(`
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `);
    }

    // 下一页
    pagination.append(`
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a>
        </li>
    `);

    // 点击事件
    pagination.find('a.page-link').off('click').on('click', function(e) {
        e.preventDefault();
        const page = parseInt($(this).data('page'));
        if (!isNaN(page) && page >= 1 && page <= totalPages && page !== currentPage) {
            loadAlertHistory(page);
        }
    });
}

// 系统状态和YOLOWorld类别更新
function updateSystemStatus() {
    $.ajax({
        url: '/api/status',
        type: 'GET',
        success: function(response) {
            $('#system-status').html(`系统状态: <span class="badge bg-success">正常</span>`);
            if (response.success && response.status) {
                if (response.status.current_model && response.status.current_model !== $('#modelSelect').val()) {
                    $('#modelSelect').val(response.status.current_model).trigger('change');
                }
                if (response.status.yoloworld_class && response.status.yoloworld_class !== $('#yoloWorldClassSelect').val()) {
                    $('#yoloWorldClassSelect').val(response.status.yoloworld_class);
                }
                $('#intervalInput').val(response.status.detection_interval);
            }
        },
        error: function(xhr) {
            $('#system-status').html(`系统状态: <span class="badge bg-danger">异常</span>`);
            console.error('获取系统状态失败:', xhr.responseText);
        }
    });
}

function updateYoloWorldClass() {
    const selectedClass = $('#yoloWorldClassSelect').val();
    $.ajax({
        url: '/api/yoloworld_class',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ class: selectedClass }),
        success: function(response) {
            if (response.success) {
                showToast(`已设置检测类别: ${selectedClass === 'all' ? '全部' : selectedClass}`);
            } else {
                showToast('设置类别失败', 'error');
            }
        },
        error: function(xhr) {
            showToast('设置类别失败', 'error');
        }
    });
}

// 通用提示工具
function showToast(message, type = 'success') {
    // 暂时用alert，可以替换成更美观的Toast库
    console.log(`[${type.toUpperCase()}] ${message}`);
    // alert(message);
}