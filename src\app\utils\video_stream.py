import cv2
import time
import threading
import queue
import logging
import numpy as np
from pathlib import Path
from src.app.config.config import DETECTION_INTERVAL

logger = logging.getLogger(__name__)

class StreamSource:
    """视频流源的基类"""
    def __init__(self, source_id, name=None):
        self.source_id = source_id
        self.name = name if name else f"Stream_{source_id}"
        self.is_running = False
        self.lock = threading.RLock()
        self.frame_queue = queue.Queue(maxsize=5)
        self.latest_frame = None
        self.latest_time = 0
        self.fps = 0
        self.dropped_frames = 0
        self.read_thread = None
        # 添加检测结果属性和锁
        self.latest_detections = None
        self.detection_lock = threading.RLock()
        # 添加检测结果时间戳和有效期
        self.detection_timestamp = 0  # 最近一次检测的时间戳
        self.detection_valid_period = DETECTION_INTERVAL  # 检测结果的有效期与系统检测间隔同步
        # 添加预处理支持
        self.preprocessor = None
        self.preprocess_enabled = False
        self.original_frame = None  # 保存原始帧（未经预处理的）
    
    def set_preprocessor(self, preprocessor, enabled=True):
        """
        设置图像预处理器
        Args:
            preprocessor: 预处理器实例
            enabled: 是否启用预处理
        """
        with self.lock:
            self.preprocessor = preprocessor
            self.preprocess_enabled = enabled
            logger.info(f"数据源 {self.name} 预处理{'启用' if enabled else '禁用'}")
    
    def start(self):
        """启动视频流"""
        with self.lock:
            if not self.is_running:
                self.is_running = True
                self.read_thread = threading.Thread(target=self._read_frames, daemon=True)
                self.read_thread.start()
                logger.info(f"启动数据流 {self.name}")
                return True
            return False
    
    def stop(self):
        """停止视频流"""
        with self.lock:
            if self.is_running:
                self.is_running = False
                if self.read_thread and self.read_thread.is_alive():
                    self.read_thread.join(timeout=1.0)
                self._cleanup()
                logger.info(f"停止数据流 {self.name}")
                return True
            return False
    
    def get_frame(self, block=False, timeout=None, original=False):
        """
        获取最新的帧
        Args:
            block: 是否阻塞等待
            timeout: 等待超时时间(秒)
            original: 是否返回原始帧（未经预处理的）
        Returns:
            (帧, 时间戳)元组，如果没有可用帧则返回(None, 0)
        """
        try:
            with self.lock:
                if not self.is_running:
                    return None, 0
                
                if block:
                    try:
                        frame, timestamp = self.frame_queue.get(timeout=timeout)
                        return (self.original_frame, timestamp) if original and self.original_frame is not None else (frame, timestamp)
                    except queue.Empty:
                        return None, 0
                else:
                    return (self.original_frame, self.latest_time) if original and self.original_frame is not None else (self.latest_frame, self.latest_time)
        except Exception as e:
            logger.error(f"获取帧时出错: {e}")
            return None, 0
    
    def process_frame(self, frame):
        """
        对帧应用预处理
        Args:
            frame: 输入帧
        Returns:
            处理后的帧
        """
        if frame is None:
            return None
            
        if self.preprocess_enabled and self.preprocessor:
            try:
                # 保存原始帧
                self.original_frame = frame.copy()
                # 应用预处理
                return self.preprocessor.preprocess(frame)
            except Exception as e:
                logger.error(f"帧预处理失败: {e}")
                return frame
        return frame
    
    def _read_frames(self):
        """读取帧的线程方法(由子类实现)"""
        raise NotImplementedError("子类必须实现此方法")
    
    def _cleanup(self):
        """清理资源(由子类实现)"""
        # 清空队列
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except:
                pass
        
        self.latest_frame = None
        self.latest_time = 0
        self.original_frame = None
    
    def status(self):
        """获取视频流状态"""
        with self.lock:
            return {
                'id': self.source_id,
                'name': self.name,
                'running': self.is_running,
                'fps': self.fps,
                'dropped_frames': self.dropped_frames,
                'queue_size': self.frame_queue.qsize(),
                'last_frame_time': self.latest_time,
                'preprocessing_enabled': self.preprocess_enabled
            }

class WebcamSource(StreamSource):
    """摄像头视频源"""
    def __init__(self, camera_id, name=None, width=640, height=480, fps=30):
        # 创建简化ID，与其他源类似
        source_id = f"webcam_{camera_id}"
        name = name if name else f"摄像头_{camera_id}"
        super().__init__(source_id=source_id, name=name)
        self.camera_id = camera_id
        self.width = width
        self.height = height
        self.target_fps = fps
        self.cap = None
        self.retry_count = 0
        self.max_retries = 10  # 增加重试次数
        self.init_successful = False
        # 创建一个占位符图像
        self.placeholder_frame = self._create_placeholder_frame()
        # 尝试的后端列表，按优先级排序
        self.backends = [
            (cv2.CAP_DSHOW, "DirectShow"),
            (None, "默认"),
            (cv2.CAP_MSMF, "Media Foundation"),
            (cv2.CAP_V4L2, "V4L2"),
            (cv2.CAP_ANY, "自动选择")
        ]
        self.current_backend_index = 0
        # 增加性能优化参数
        self.process_every_n_frames = 2  # 每2帧处理一次，跳帧优化
        self.current_frame_count = 0 
        self.downsample_before_process = True  # 处理前降采样
        self.max_process_resolution = (480, 360)  # 处理分辨率上限
        self.last_processed_frame = None  # 缓存最近处理结果
    
    def _create_placeholder_frame(self):
        """创建等待连接的占位符图像"""
        frame = np.zeros((self.height, self.width, 3), dtype=np.uint8)
        # 添加英文文本避免中文显示问题
        text = "Connecting to camera..."
        font = cv2.FONT_HERSHEY_SIMPLEX
        text_size = cv2.getTextSize(text, font, 0.7, 2)[0]
        text_x = int((self.width - text_size[0]) / 2)
        text_y = int((self.height + text_size[1]) / 2)
        cv2.putText(frame, text, (text_x, text_y), font, 0.7, (255, 255, 255), 2)
        return frame
    
    def start(self):
        """启动视频流 - 重写以预先初始化摄像头"""
        with self.lock:
            if not self.is_running:
                self.is_running = True
                # 设置初始帧为占位符
                self.latest_frame = self.placeholder_frame.copy()
                self.latest_time = time.time()
                # 将占位符放入队列
                try:
                    self.frame_queue.put((self.placeholder_frame.copy(), self.latest_time), block=False)
                except queue.Full:
                    pass
                
                # 清空最新检测结果
                with self.detection_lock:
                    self.latest_detections = None
                # 启动读取线程
                self.read_thread = threading.Thread(target=self._read_frames, daemon=True)
                self.read_thread.start()
                logger.info(f"启动数据流 {self.name}")
                return True
            return False
    
    def _try_open_camera(self):
        """尝试使用不同的后端打开摄像头"""
        if self.cap and self.cap.isOpened():
            self.cap.release()
            self.cap = None
        
        success = False
        backend_tried = 0
        # 循环尝试所有后端，直到找到一个可用的
        while not success and backend_tried < len(self.backends):
            backend_index = (self.current_backend_index + backend_tried) % len(self.backends)
            backend, name = self.backends[backend_index]

            try:
                logger.info(f"尝试使用 {name} 后端打开摄像头 {self.camera_id}")
                if backend is None:
                    self.cap = cv2.VideoCapture(self.camera_id)
                else:
                    self.cap = cv2.VideoCapture(self.camera_id, backend)
                
                # 验证摄像头是否正常打开
                if self.cap.isOpened():
                    # 如果能读取第一帧，则认为打开成功
                    ret, test_frame = self.cap.read()
                    if ret and test_frame is not None:
                        # 设置分辨率和帧率
                        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
                        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
                        self.cap.set(cv2.CAP_PROP_FPS, self.target_fps)
                        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 3)  # 设置较小的缓冲区减少延迟
                        success = True
                        # 保存成功的后端索引
                        self.current_backend_index = backend_index
                        logger.info(f"成功使用 {name} 后端打开摄像头 {self.camera_id}")
                        break
                    else:
                        self.cap.release()
                        logger.warning(f"{name} 后端已打开但无法获取帧")
            except Exception as e:
                logger.warning(f"{name} 后端打开失败: {e}")
            
            backend_tried += 1
            time.sleep(0.2)  # 短暂休息，让系统缓一缓
        
        return success
    
    def _read_frames(self):
        """读取摄像头帧的线程方法"""
        try:
            # 尝试打开摄像头
            camera_opened = self._try_open_camera()
            
            if not camera_opened:
                logger.error(f"无法打开摄像头 {self.camera_id}，所有后端都失败")
                self.is_running = False
                return
            
            # 丢弃前几帧，这些帧通常是黑屏或不稳定的
            for _ in range(5):
                self.cap.read()
                time.sleep(0.01)
            
            frame_count = 0
            start_time = time.time()
            frame_time = start_time
            
            while self.is_running:
                ret, frame = self.cap.read()
                current_time = time.time()
                
                if not ret or frame is None:
                    self.retry_count += 1
                    logger.warning(f"摄像头 {self.camera_id} 无法读取帧 (尝试 {self.retry_count}/{self.max_retries})")
                    
                    if self.retry_count >= self.max_retries:
                        logger.error(f"摄像头 {self.camera_id} 读取失败次数过多，尝试重新初始化")
                        self.retry_count = 0
                        if self._try_open_camera():
                            # 如果重新打开成功，继续循环
                            continue
                        else:
                            # 所有尝试都失败
                            logger.error(f"摄像头 {self.camera_id} 无法恢复，停止")
                            self.is_running = False
                            return
                    
                    # 短暂休息后继续尝试
                    time.sleep(0.5)
                    continue
                
                # 成功读取到帧，重置重试计数
                self.retry_count = 0
                self.init_successful = True
                
                frame_count += 1
                elapsed = current_time - start_time
                
                if elapsed > 0:
                    self.fps = frame_count / elapsed
                
                # 应用预处理
                processed_frame = self.process_frame(frame)
                
                # 更新最新帧
                with self.lock:
                    self.latest_frame = processed_frame
                    self.latest_time = current_time
                
                # 加入队列，如果队列满则丢弃
                try:
                    self.frame_queue.put((processed_frame, current_time), block=False)
                except queue.Full:
                    self.dropped_frames += 1
                
                # 控制帧率
                if self.target_fps > 0:
                    sleep_time = max(0, 1.0/self.target_fps - (time.time() - frame_time))
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                    frame_time = time.time()
        
        except Exception as e:
            logger.error(f"摄像头 {self.camera_id} 读取帧时出错: {e}")
        finally:
            if self.cap and self.cap.isOpened():
                self.cap.release()
    
    def _cleanup(self):
        """清理摄像头资源"""
        super()._cleanup()
        if self.cap and self.cap.isOpened():
            self.cap.release()
            self.cap = None
            
        # 清空检测结果
        with self.detection_lock:
            self.latest_detections = None

    def process_frame(self, frame):
        """
        对帧应用预处理，增加性能优化
        Args:
            frame: 输入帧
        Returns:
            处理后的帧
        """
        if frame is None:
            return None
            
        # 保存原始帧
        self.original_frame = frame.copy()

        # 如果不需要预处理，直接返回原帧
        if not self.preprocess_enabled or self.preprocessor is None:
            return frame

        # 跳帧处理优化：只处理每N帧中的1帧
        self.current_frame_count += 1
        if self.current_frame_count % self.process_every_n_frames != 0:
            # 如果有上一帧处理结果，返回缓存的结果
            if self.last_processed_frame is not None:
                return self.last_processed_frame
            else:
                return frame
                
        try:
            processed_frame = None
            
            # 降低处理分辨率以提高性能
            if self.downsample_before_process and (frame.shape[1] > self.max_process_resolution[0] or 
                                                   frame.shape[0] > self.max_process_resolution[1]):
                # 计算缩放比例
                scale = min(self.max_process_resolution[0] / frame.shape[1],
                            self.max_process_resolution[1] / frame.shape[0])
                # 临时降低分辨率进行处理
                small_frame = cv2.resize(frame, (0, 0), fx=scale, fy=scale, 
                                        interpolation=cv2.INTER_AREA)
                # 处理低分辨率图像
                small_processed = self.preprocessor.preprocess(small_frame)
                # 将处理后的低分辨率图像缩放回原始大小
                processed_frame = cv2.resize(small_processed, (frame.shape[1], frame.shape[0]),
                                           interpolation=cv2.INTER_LINEAR)
            else:
                # 直接处理原始分辨率
                processed_frame = self.preprocessor.preprocess(frame)
            # 缓存处理结果
            self.last_processed_frame = processed_frame
            return processed_frame
            
        except Exception as e:
            logger.error(f"摄像头帧预处理失败: {e}")
            return frame

class VideoFileSource(StreamSource):
    """视频文件源"""
    def __init__(self, video_path, name=None, loop=False, fps=None, on_complete=None):
        # 使用文件名作为ID而不是完整路径
        file_name = Path(video_path).name
        source_id = f"video_{file_name}"
        name = name if name else file_name
        super().__init__(source_id=source_id, name=name)
        self.video_path = video_path
        self.loop = loop
        self.target_fps = fps
        self.cap = None
        self.original_fps = 0
        self.on_complete = on_complete  # 视频播放完成的回调函数
    
    def _read_frames(self):
        """读取视频文件帧的线程方法"""
        try:
            while self.is_running:
                self.cap = cv2.VideoCapture(self.video_path)
                
                if not self.cap.isOpened():
                    logger.error(f"无法打开数据文件 {self.video_path}")
                    self.is_running = False
                    return
                
                # 获取原视频的FPS
                self.original_fps = self.cap.get(cv2.CAP_PROP_FPS)
                logger.info(f"视频 {self.name} FPS: {self.original_fps}")

                # 如果没有指定FPS，则使用原视频的FPS
                fps_to_use = self.target_fps if self.target_fps else self.original_fps
                
                frame_count = 0
                start_time = time.time()
                frame_time = start_time
                
                while self.is_running:
                    ret, frame = self.cap.read()
                    current_time = time.time()
                    
                    if not ret or frame is None:
                        logger.info(f"视频 {self.name} 播放完毕")
                        
                        # 设置最后一帧为播放结束提示帧
                        if not self.loop:
                            end_message_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                            # 使用英文避免乱码
                            cv2.putText(end_message_frame, f"{self.name} - Playback completed", 
                                      (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                            
                            with self.lock:
                                self.latest_frame = end_message_frame
                                self.latest_time = current_time
                            
                            # 加入队列最后一帧
                            try:
                                self.frame_queue.put((end_message_frame.copy(), current_time), block=False)
                            except queue.Full:
                                pass
                        
                        if self.loop:
                            # 如果循环播放，则重新打开视频
                            break
                        else:
                            # 非循环模式，视频播放结束，停止线程
                            self.is_running = False
                            
                            # 如果有回调函数，通知视频播放完成
                            if callable(self.on_complete):
                                try:
                                    self.on_complete(self.source_id)
                                except Exception as e:
                                    logger.error(f"数据完成回调函数执行出错: {e}")
                            return
                    
                    frame_count += 1
                    elapsed = current_time - start_time
                    
                    if elapsed > 0:
                        self.fps = frame_count / elapsed
                    
                    # 应用预处理
                    processed_frame = self.process_frame(frame)
                    
                    # 更新最新帧
                    with self.lock:
                        self.latest_frame = processed_frame
                        self.latest_time = current_time
                    
                    # 加入队列，如果队列满则丢弃
                    try:
                        self.frame_queue.put((processed_frame, current_time), block=False)
                    except queue.Full:
                        self.dropped_frames += 1
                    
                    # 控制帧率
                    if fps_to_use > 0:
                        sleep_time = max(0, 1.0/fps_to_use - (time.time() - frame_time))
                        if sleep_time > 0:
                            time.sleep(sleep_time)
                        frame_time = time.time()
                
                # 如果是循环播放，继续循环
                if not self.loop or not self.is_running:
                    break
        
        except Exception as e:
            logger.error(f"视频 {self.name} 读取帧时出错: {e}")
        finally:
            if self.cap and self.cap.isOpened():
                self.cap.release()
    
    def _cleanup(self):
        """清理视频文件资源"""
        super()._cleanup()
        if self.cap and self.cap.isOpened():
            self.cap.release()
            self.cap = None

class ImageSource(StreamSource):
    """图像文件源"""
    def __init__(self, image_path, name=None):
        # 使用文件名作为ID而不是完整路径
        file_name = Path(image_path).name
        source_id = f"image_{file_name}"
        name = name if name else file_name
        super().__init__(source_id=source_id, name=name)
        self.image_path = image_path
        self.image = None
    
    def _read_frames(self):
        """读取图像文件"""
        try:
            self.image = cv2.imread(self.image_path)
            
            if self.image is None:
                logger.error(f"无法加载图像 {self.image_path}")
                self.is_running = False
                return
            
            current_time = time.time()
            
            # 应用预处理
            processed_image = self.process_frame(self.image)
            
            # 更新最新帧
            with self.lock:
                self.latest_frame = processed_image
                self.latest_time = current_time
            
            # 加入队列
            self.frame_queue.put((processed_image, current_time), block=True)
            
            # 对于图像源，只生成一帧然后睡眠，保持线程活跃
            while self.is_running:
                time.sleep(0.1)
        
        except Exception as e:
            logger.error(f"图像 {self.image_path} 加载时出错: {e}")
    
    def _cleanup(self):
        """清理图像资源"""
        super()._cleanup()
        self.image = None

class NetworkStreamSource(StreamSource):
    """网络视频流源，支持RTSP、HTTP等网络视频流"""
    def __init__(self, stream_url, name=None, reconnect_interval=5, connection_timeout=10):
        source_id = f"network_{hash(stream_url) % 10000}"
        name = name if name else f"网络数据流_{stream_url[-10:]}"
        super().__init__(source_id=source_id, name=name)
        self.stream_url = stream_url
        self.reconnect_interval = reconnect_interval  # 重连间隔（秒）
        self.connection_timeout = connection_timeout  # 连接超时（秒）
        self.cap = None
        self.last_reconnect_time = 0
        self.connection_attempts = 0
        self.max_connection_attempts = 5
        self.frame_count = 0
        self.last_fps_time = time.time()
        # 创建一个占位符图像
        self.placeholder_frame = self._create_placeholder_frame()
        # 添加连接状态和最后一次成功帧的时间
        self.connection_status = "Disconnected"
        self.last_successful_frame_time = 0
        # 添加额外的重试配置
        self.use_tcp_for_rtsp = True  # 如果是RTSP，默认使用TCP而不是UDP
        self.alternate_protocol_tried = False  # 是否已经尝试了备用协议
    
    def _create_placeholder_frame(self, width=640, height=480):
        """创建等待连接的占位符图像"""
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        text = "Connecting to video stream..."
        font = cv2.FONT_HERSHEY_SIMPLEX
        text_size = cv2.getTextSize(text, font, 0.7, 2)[0]
        text_x = int((width - text_size[0]) / 2)
        text_y = int((height + text_size[1]) / 2)
        cv2.putText(frame, text, (text_x, text_y), font, 0.7, (255, 255, 255), 2)
        return frame
    
    def _connect_to_stream(self):
        """连接到网络视频流，支持更多选项和重试机制"""
        try:
            if self.cap is not None and self.cap.isOpened():
                self.cap.release()
            
            logger.info(f"正在连接到网络数据流: {self.stream_url}")
            self.connection_attempts += 1
            self.connection_status = "Connecting"
            
            # 构建OpenCV VideoCapture选项
            options = {}
            
            # 设置RTSP选项，优先使用TCP (更稳定)
            if self.stream_url.lower().startswith('rtsp://'):
                if self.use_tcp_for_rtsp:
                    options[cv2.CAP_PROP_FOURCC] = cv2.VideoWriter_fourcc(*'H264')
                    options[cv2.CAP_PROP_RTSP_TRANSPORT] = cv2.CAP_RTSP_TRANSPORT_TCP
                else:
                    options[cv2.CAP_PROP_RTSP_TRANSPORT] = cv2.CAP_RTSP_TRANSPORT_UDP
                
                # 增加缓冲区大小，提高稳定性
                options[cv2.CAP_PROP_BUFFERSIZE] = 3
            
            # 设置连接超时，避免长时间等待
            options[cv2.CAP_PROP_OPEN_TIMEOUT_MSEC] = self.connection_timeout * 1000
            options[cv2.CAP_PROP_READ_TIMEOUT_MSEC] = self.connection_timeout * 1000
            
            # 创建VideoCapture对象并应用选项
            self.cap = cv2.VideoCapture(self.stream_url, cv2.CAP_ANY)
            for key, value in options.items():
                self.cap.set(key, value)
            
            # 检查连接是否成功
            if not self.cap.isOpened():
                logger.error(f"无法连接到网络数据流: {self.stream_url}")
                
                # 如果是RTSP且未尝试过备用协议，则尝试另一种协议
                if self.stream_url.lower().startswith('rtsp://') and not self.alternate_protocol_tried:
                    self.use_tcp_for_rtsp = not self.use_tcp_for_rtsp
                    self.alternate_protocol_tried = True
                    logger.info(f"尝试使用{'TCP' if self.use_tcp_for_rtsp else 'UDP'}协议重新连接RTSP流")
                    return self._connect_to_stream()  # 递归尝试备用协议
                
                self.connection_status = "Failed"
                return False
            
            # 连接成功
            logger.info(f"成功连接到网络数据流: {self.stream_url}")
            self.connection_attempts = 0
            self.connection_status = "Connected"
            self.alternate_protocol_tried = False  # 重置备用协议尝试标志
            return True
        except Exception as e:
            logger.error(f"连接网络数据流时出错: {e}")
            self.connection_status = "Error"
            return False
    
    def _read_frames(self):
        """读取网络视频流帧，增强重连和稳定性"""
        try:
            # 首次连接
            if not self._connect_to_stream():
                # 发送占位符帧
                with self.lock:
                    self.latest_frame = self.placeholder_frame
                    self.latest_time = time.time()
                self.frame_queue.put((self.placeholder_frame, self.latest_time), block=False)
                time.sleep(self.reconnect_interval)
                
                # 如果首次连接失败，继续尝试连接
                while self.is_running and self.connection_status != "Connected":
                    if self._connect_to_stream():
                        break
                    time.sleep(self.reconnect_interval)
                
                # 如果所有尝试失败，退出
                if self.connection_status != "Connected":
                    return
            
            # 重置帧计数
            self.frame_count = 0
            self.last_fps_time = time.time()
            no_frame_count = 0  # 连续无帧计数
            
            # 主循环
            while self.is_running:
                current_time = time.time()
                
                # 检查连接状态
                if not self.cap or not self.cap.isOpened() or self.connection_attempts >= self.max_connection_attempts:
                    logger.warning(f"数据流连接断开或多次尝试失败，尝试重新连接: {self.stream_url}")
                    # 重置连接状态
                    self.connection_status = "Reconnecting"
                    # 如果距离上次重连尝试已经过了足够时间，尝试重连
                    if (current_time - self.last_reconnect_time) > self.reconnect_interval:
                        self.last_reconnect_time = current_time
                        # 发送占位符帧，表示正在重连
                        with self.lock:
                            self.latest_frame = self.placeholder_frame
                            self.latest_time = current_time
                        
                        try:
                            if not self.frame_queue.full():
                                self.frame_queue.put((self.placeholder_frame, current_time), block=False)
                        except:
                            pass
                        
                        # 尝试重新连接
                        if not self._connect_to_stream():
                            time.sleep(self.reconnect_interval)
                            continue

                # 读取帧
                try:
                    ret, frame = self.cap.read()
                except Exception as e:
                    logger.error(f"读取数据流帧时出错: {e}")
                    ret, frame = False, None
                
                if not ret or frame is None:
                    no_frame_count += 1
                    # 如果连续多次无法获取帧，可能是连接问题
                    if no_frame_count > 10:
                        logger.warning(f"连续{no_frame_count}次无法获取帧，可能需要重新连接")
                        # 尝试释放并重新连接
                        if self.cap:
                            try:
                                self.cap.release()
                            except:
                                pass
                        
                        self.cap = None
                        self.connection_status = "Disconnected"
                        no_frame_count = 0
                    
                    time.sleep(0.1)
                    continue
                
                # 成功获取帧，重置计数器
                no_frame_count = 0
                self.last_successful_frame_time = current_time
                # 应用预处理
                processed_frame = self.process_frame(frame)
                # 更新最新帧
                with self.lock:
                    self.latest_frame = processed_frame
                    self.latest_time = current_time
                
                # 计算FPS
                self.frame_count += 1
                if current_time - self.last_fps_time >= 1.0:  # 每秒更新一次FPS
                    self.fps = self.frame_count / (current_time - self.last_fps_time)
                    self.frame_count = 0
                    self.last_fps_time = current_time
                
                # 加入队列，丢弃旧帧如果队列满
                try:
                    if self.frame_queue.full():
                        try:
                            self.frame_queue.get_nowait()
                            self.dropped_frames += 1
                        except:
                            pass
                    self.frame_queue.put((processed_frame, current_time), block=False)
                except:
                    pass
                
                # 控制帧率
                time.sleep(0.01)
        
        except Exception as e:
            logger.error(f"网络数据流 {self.stream_url} 读取帧时出错: {e}")
        finally:
            if self.cap and self.cap.isOpened():
                self.cap.release()
                self.cap = None
    
    def _cleanup(self):
        """清理网络视频流资源，增加错误处理"""
        try:
            # 首先调用父类清理方法
            super()._cleanup()
            # 确保视频捕获对象被正确关闭
            if self.cap is not None:
                try:
                    if self.cap.isOpened():
                        self.cap.release()
                except Exception as e:
                    logger.error(f"释放网络数据流资源出错: {e}")
                finally:
                    self.cap = None
            
            # 重置连接相关状态
            self.connection_attempts = 0
            self.last_reconnect_time = 0
            self.connection_status = "Disconnected"
            # 清空帧计数
            self.frame_count = 0
            self.fps = 0
            
            logger.info(f"网络数据流 {self.stream_url} 资源已清理")
        except Exception as e:
            logger.error(f"清理网络数据流资源时出错: {e}")
            # 即使出错，也确保cap被设置为None
            self.cap = None
    
    def status(self):
        """获取网络视频流状态，包含更多信息"""
        status = super().status()
        status.update({
            'url': self.stream_url,
            'connection_status': self.connection_status,
            'connection_attempts': self.connection_attempts,
            'last_frame_time': self.last_successful_frame_time,
            'seconds_since_last_frame': time.time() - self.last_successful_frame_time if self.last_successful_frame_time > 0 else -1,
            'protocol': 'TCP' if self.use_tcp_for_rtsp else 'UDP' if self.stream_url.lower().startswith('rtsp://') else 'N/A'
        })
        return status

class StreamManager:
    """视频流管理器，管理多个视频流"""
    def __init__(self, max_sources=10):
        self.sources = {}
        self.max_sources = max_sources
        self.lock = threading.RLock()
    
    def add_source(self, source):
        """
        添加视频流源
        Args:
            source: StreamSource实例
        Returns:
            成功返回True，失败返回False
        """
        with self.lock:
            if len(self.sources) >= self.max_sources:
                logger.error(f"超过最大数据流数量限制: {self.max_sources}")
                return False
            
            if source.source_id in self.sources:
                logger.warning(f"数据流已存在: {source.source_id}")
                return False
            
            self.sources[source.source_id] = source
            source.start()
            return True
    
    def remove_source(self, source_id):
        """
        移除视频流源
        Args:
            source_id: 视频流ID
        Returns:
            成功返回True，失败返回False
        """
        with self.lock:
            if source_id in self.sources:
                try:
                    source = self.sources[source_id]
                    # 记录源类型和其他信息（用于网络流特殊处理）
                    is_network_stream = isinstance(source, NetworkStreamSource)
                    stream_url = getattr(source, 'stream_url', None) if is_network_stream else None

                    # 先从字典中移除，避免其他线程尝试访问
                    del self.sources[source_id]
                    
                    # 创建单独的线程来停止源，避免阻塞主线程
                    def stop_source_thread(src, src_id, is_network=False, url=None):
                        try:
                            logger.info(f"开始停止数据源: {src.name}")
                            
                            # 对于网络流，记录更多信息
                            if is_network and url:
                                logger.info(f"停止网络数据流，URL: {url}")
                            
                            # 设置较短的超时时间，防止线程卡住
                            stop_thread = threading.Thread(target=src.stop, daemon=True)
                            stop_thread.start()
                            stop_thread.join(timeout=3.0)  # 等待3秒
                            
                            if stop_thread.is_alive():
                                logger.warning(f"数据源 {src.name} 停止超时，强制清理资源")
                            
                            # 无论是否超时，都强制清理资源
                            try:
                                src._cleanup()
                                # 对于网络流，确保连接关闭
                                if is_network and hasattr(src, 'cap') and src.cap:
                                    try:
                                        src.cap.release()
                                        src.cap = None
                                        logger.info(f"强制关闭网络流连接: {url}")
                                    except Exception as e:
                                        logger.error(f"强制关闭网络流连接时出错: {e}")
                            except Exception as e:
                                logger.error(f"清理数据源时出错: {e}")
                                
                            logger.info(f"数据源 {src_id} 已完全停止和清理")
                        except Exception as e:
                            logger.error(f"停止数据源出错: {e}")
                    
                    threading.Thread(
                        target=stop_source_thread, 
                        args=(source, source_id, is_network_stream, stream_url), 
                        daemon=True
                    ).start()
                    
                    logger.info(f"已从流管理器中移除数据源: {source_id}")
                    return True
                except Exception as e:
                    logger.error(f"移除数据源时出错: {e}")
                    # 即使出错也返回True，表示从管理器中移除了
                    return True
            else:
                logger.debug(f"数据源不存在，无需移除: {source_id}")
            return False
    
    def get_source(self, source_id):
        """获取视频流源"""
        with self.lock:
            source = self.sources.get(source_id)
            if source is None and logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"请求的数据源不存在: {source_id}")
            return source
    
    def get_frame(self, source_id, block=False, timeout=None, original=False):
        """
        获取指定视频流的最新帧
        Args:
            source_id: 视频流ID
            block: 是否阻塞等待
            timeout: 等待超时时间(秒)
            original: 是否返回原始帧（未经预处理的）
        Returns:
            (帧, 时间戳)元组
        """
        source = self.get_source(source_id)
        if source:
            return source.get_frame(block, timeout, original)
        return None, 0
    
    def get_all_sources(self):
        """获取所有视频流源的ID和名称"""
        with self.lock:
            return [(s.source_id, s.name) for s in self.sources.values()]
    
    def get_all_status(self):
        """获取所有视频流的状态"""
        with self.lock:
            return {s.source_id: s.status() for s in self.sources.values()}
    
    def stop_all(self):
        """停止所有视频流"""
        with self.lock:
            for source in list(self.sources.values()):
                source.stop()
            self.sources.clear()
    
    def create_and_add_webcam(self, camera_id, name=None, width=640, height=480, fps=30):
        """创建并添加摄像头源"""
        source = WebcamSource(camera_id, name, width, height, fps)
        return self.add_source(source), source.source_id
    
    def create_and_add_video(self, video_path, name=None, loop=False, fps=None, on_complete=None):
        """创建并添加视频文件源"""
        source = VideoFileSource(video_path, name, loop, fps, on_complete)
        return self.add_source(source), source.source_id
    
    def create_and_add_image(self, image_path, name=None):
        """创建并添加图像文件源"""
        source = ImageSource(image_path, name)
        return self.add_source(source), source.source_id
    
    def create_and_add_network_stream(self, stream_url, name=None, reconnect_interval=5, connection_timeout=10):
        """创建并添加网络视频流源"""
        source = NetworkStreamSource(stream_url, name, reconnect_interval, connection_timeout)
        return self.add_source(source), source.source_id 