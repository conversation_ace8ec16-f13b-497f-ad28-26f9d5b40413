# 日志重复输出问题解决方案

## 问题描述
编译的exe启动时会输出两遍相同的日志，导致控制台输出混乱。

## 问题原因分析

### 1. 重复的日志配置
- `run.py` 中配置了一次 `logging.basicConfig`
- `core.py` 中的 `setup_logging()` 方法又配置了一次
- 导致日志处理器被重复添加

### 2. Flask开发模式的重载器
- 当 `use_reloader=True` 时，Flask会启动两个进程
- 主进程和重载器进程都会输出日志
- 在生产环境（exe）中不需要重载器

### 3. 日志处理器累积
- 多次调用 `logging.basicConfig` 可能导致处理器累积
- 没有清理旧的处理器

## 解决方案

### 1. 统一日志配置
**修改前**：
```python
# run.py 和 core.py 都有日志配置
logging.basicConfig(...)  # 重复配置
```

**修改后**：
```python
# 只在 run.py 中配置一次
def main():
    # 清除现有的日志处理器，避免重复
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 配置日志格式和级别（只在这里配置一次）
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        force=True  # 强制重新配置，避免重复
    )
```

### 2. 移除重复配置
- 删除了 `core.py` 中的 `setup_logging()` 方法
- 移除了 `AppCore.__init__()` 中对 `setup_logging()` 的调用

### 3. 关闭Flask重载器
**修改前**：
```python
app.run(use_reloader=True)  # 会启动两个进程
```

**修改后**：
```python
app.run(use_reloader=False)  # 关闭重载器避免重复日志
```

### 4. 优化日志级别
```python
# 降低不必要模块的日志级别
logging.getLogger('werkzeug').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('PIL').setLevel(logging.WARNING)
logging.getLogger('matplotlib').setLevel(logging.WARNING)
logging.getLogger('ultralytics').setLevel(logging.WARNING)
```

## 修改的文件

### 1. run.py
- 增强了日志配置，添加了处理器清理
- 关闭了Flask重载器
- 添加了更多模块的日志级别控制

### 2. src/app/core.py
- 移除了 `setup_logging()` 方法
- 移除了构造函数中的日志配置调用

## 验证方法

### 1. 运行测试
创建测试脚本验证日志是否重复：
```python
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 测试日志
logger = logging.getLogger('test')
logger.info("测试消息")  # 应该只出现一次
```

### 2. 检查exe输出
- 启动编译后的exe
- 观察控制台输出
- 每条日志消息应该只出现一次

## 预期效果

**修改前**：
```
2025-07-23 11:18:45,437 - src.app.core - INFO - 邮件告警配置...
2025-07-23 11:18:45,437 - src.app.core - INFO - 邮件告警配置...  # 重复
```

**修改后**：
```
2025-07-23 11:18:45,437 - src.app.core - INFO - 邮件告警配置...  # 只出现一次
```

## 注意事项

1. **开发环境**：如果需要热重载功能，可以临时设置 `use_reloader=True`
2. **生产环境**：建议保持 `use_reloader=False` 以避免不必要的进程
3. **日志级别**：可以根据需要调整各模块的日志级别
4. **性能影响**：减少重复日志输出可以提高性能，特别是在高频日志场景下

## 总结

通过以上修改，解决了日志重复输出的问题：
- ✅ 统一了日志配置，避免重复设置
- ✅ 清理了旧的日志处理器
- ✅ 关闭了不必要的Flask重载器
- ✅ 优化了各模块的日志级别

现在编译的exe启动时应该只会输出一遍日志，控制台输出更加清晰。
