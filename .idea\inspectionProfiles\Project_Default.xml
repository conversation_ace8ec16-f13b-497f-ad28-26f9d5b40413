<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="18">
            <item index="0" class="java.lang.String" itemvalue="pillow" />
            <item index="1" class="java.lang.String" itemvalue="torch" />
            <item index="2" class="java.lang.String" itemvalue="requests" />
            <item index="3" class="java.lang.String" itemvalue="torchvision" />
            <item index="4" class="java.lang.String" itemvalue="ultralytics" />
            <item index="5" class="java.lang.String" itemvalue="scipy" />
            <item index="6" class="java.lang.String" itemvalue="opencv-python" />
            <item index="7" class="java.lang.String" itemvalue="PyYAML" />
            <item index="8" class="java.lang.String" itemvalue="setuptools" />
            <item index="9" class="java.lang.String" itemvalue="numpy" />
            <item index="10" class="java.lang.String" itemvalue="tqdm" />
            <item index="11" class="java.lang.String" itemvalue="pandas" />
            <item index="12" class="java.lang.String" itemvalue="easydict" />
            <item index="13" class="java.lang.String" itemvalue="tensorboard" />
            <item index="14" class="java.lang.String" itemvalue="seaborn" />
            <item index="15" class="java.lang.String" itemvalue="matplotlib" />
            <item index="16" class="java.lang.String" itemvalue="imutils" />
            <item index="17" class="java.lang.String" itemvalue="Pillow" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>