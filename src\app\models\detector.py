import os
import time
import logging
import threading
import cv2
import numpy as np
import torch
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class BaseDetector(ABC):
    """检测器基类，定义接口"""
    
    def __init__(self, model_path, confidence_threshold=0.5):
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.model = None
        self.lock = threading.RLock()
        
    @abstractmethod
    def load_model(self):
        """加载模型"""
        pass
    
    @abstractmethod
    def detect(self, image):
        """执行检测"""
        pass
    
    def release(self):
        """释放模型资源"""
        with self.lock:
            self.model = None
            torch.cuda.empty_cache()

class YOLOv8Detector(BaseDetector):
    """YOLOv8 检测器"""
    
    def load_model(self):
        """加载YOLOv8模型"""
        with self.lock:
            if self.model is None:
                try:
                    try:
                        from ultralytics import YOLO
                    except ImportError:
                        logger.warning("ultralytics模块未安装，使用模拟检测器")
                        # 创建一个简单的模拟检测器
                        class MockYOLO:
                            def __call__(self, image):
                                class MockResults:
                                    def __init__(self):
                                        self.boxes = MockBoxes()
                                        self.names = {0: "未知对象"}
                                return [MockResults()]
                                
                        class MockBoxes:
                            def __init__(self):
                                self.data = []
                                
                        self.model = MockYOLO()
                        return
                        
                    logger.info(f"加载YOLOv8模型: {self.model_path}")
                    self.model = YOLO(self.model_path)
                except Exception as e:
                    logger.error(f"加载YOLOv8模型失败: {e}")
                    # 创建一个简单的模拟检测器，避免系统崩溃
                    class MockYOLO:
                        def __call__(self, image):
                            class MockResults:
                                def __init__(self):
                                    self.boxes = MockBoxes()
                                    self.names = {0: "未知对象"}
                            return [MockResults()]
                            
                    class MockBoxes:
                        def __init__(self):
                            self.data = []
                            
                    self.model = MockYOLO()
    
    def detect(self, image):
        """
        使用YOLOv8执行滑坡和落石检测
        Args:
            image: 输入图像
        Returns:
            检测结果列表，每个结果包含边界框、置信度和类别
        """
        with self.lock:
            if self.model is None:
                self.load_model()
                
            try:
                results = self.model(image)[0]
                detections = []
                
                for result in results.boxes.data.tolist():
                    x1, y1, x2, y2, confidence, class_id = result
                    
                    if confidence >= self.confidence_threshold:
                        detections.append({
                            'box': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': float(confidence),
                            'class': int(class_id),
                            'class_name': results.names[int(class_id)]
                        })
                
                return detections
            except Exception as e:
                logger.error(f"YOLOv8检测失败: {e}")
                return []

class YOLOWorldDetector(BaseDetector):
    """YOLO-World 开集检测器"""

    def __init__(self, model_path, confidence_threshold=0.5):
        super().__init__(model_path, confidence_threshold)
        self.classes = ["fallen tree", "landslide", "road collapse", "stone"]

    def load_model(self):
        """加载YOLO-World模型"""
        with self.lock:
            if self.model is None:
                try:
                    try:
                        from ultralytics import YOLO
                        import torch
                        # 尝试禁用ultralytics的内部日志
                        from ultralytics.utils.ops import LOGGER
                        # 设置ultralytics日志级别为ERROR，只显示错误信息
                        import logging
                        LOGGER.setLevel(logging.ERROR)
                    except ImportError:
                        logger.warning("ultralytics模块未安装，使用模拟检测器")
                        # 创建一个简单的模拟检测器
                        class MockBoxes:
                            def __init__(self):
                                self.data = torch.tensor([])
                                
                        class MockResults:
                            def __init__(self):
                                self.boxes = MockBoxes()
                                self.names = {0: "fallen tree", 1: "landslide", 2: "road collapse", 3: "stone"}
                                
                        class MockYOLO:
                            def __call__(self, image, classes=None):
                                return [MockResults()]
                                
                            def predict(self, image):
                                return self(image)
                                
                            def set_classes(self, classes):
                                pass
                                
                        self.model = MockYOLO()
                        return
                        
                    logger.info(f"加载YOLO-World模型: {self.model_path}")
                    self.model = YOLO(self.model_path)
                    
                except Exception as e:
                    logger.error(f"加载YOLO-World模型失败: {e}")
                    # 创建一个简单的模拟检测器，避免系统崩溃
                    class MockBoxes:
                        def __init__(self):
                            self.data = torch.tensor([])
                            
                    class MockResults:
                        def __init__(self):
                            self.boxes = MockBoxes()
                            self.names = {0: "fallen tree", 1: "landslide", 2: "road collapse", 3: "stone"}
                            
                    class MockYOLO:
                        def __call__(self, image, classes=None):
                            return [MockResults()]
                            
                        def predict(self, image):
                            return self(image)
                            
                        def set_classes(self, classes):
                            pass
                            
                    self.model = MockYOLO()

    def detect(self, image, custom_classes=None):
        """
        使用YOLO-World执行开集检测
        Args:
            image: 输入图像
            custom_classes: 自定义类别列表，如果为None则使用默认列表
                           如果为字符串"all"则使用所有默认类别
                           如果为单个类别字符串，则会被转换为单元素列表
        Returns:
            检测结果列表
        """
        with self.lock:
            if self.model is None:
                self.load_model()

            try:
                # 处理类别参数
                if custom_classes is None or custom_classes == "all":
                    classes_to_use = self.classes.copy()
                    logger.info(f"检测所有目标")
                elif isinstance(custom_classes, str):
                    classes_to_use = [custom_classes]
                    logger.info(f"设置检测目标: {custom_classes}")
                else:
                    classes_to_use = list(custom_classes)
                    logger.info(f"设置检测目标: {', '.join(classes_to_use)}")

                # 设置YOLO-World类别
                try:
                    self.model.set_classes(classes_to_use)
                except Exception as e:
                    logger.debug(f"设置类别详情: {e}")

                # 使用predict进行预测，禁用模型的verbose输出
                try:
                    results = self.model.predict(image, verbose=False)
                except:
                    # 如果不支持verbose参数，尝试标准调用
                    results = self.model.predict(image)

                detections = []

                # 解析结果
                if results and len(results) > 0:
                    result = results[0]

                    if hasattr(result, 'boxes') and hasattr(result.boxes, 'data'):
                        boxes_data = result.boxes.data

                        if len(boxes_data) > 0:
                            # 确保boxes_data是可迭代的
                            if hasattr(boxes_data, 'tolist'):
                                boxes_list = boxes_data.tolist()
                            else:
                                # 如果不能调用tolist()，尝试直接迭代
                                boxes_list = list(boxes_data)

                            for box in boxes_list:
                                if len(box) >= 6:  # 确保box包含足够的数据
                                    x1, y1, x2, y2, confidence, class_id = box[:6]

                                    if confidence >= self.confidence_threshold:
                                        class_index = int(class_id)

                                        # 1. 获取模型识别出的真实类别名称
                                        if hasattr(result, 'names') and class_index in result.names:
                                            class_name = result.names[class_index]
                                        else:
                                            # 后备逻辑
                                            if 0 <= class_index < len(self.classes):
                                                class_name = self.classes[class_index]
                                            else:
                                                class_name = f"未知({class_index})"

                                        # 2. 检查类别是否匹配用户选择，不匹配则跳过
                                        should_add = True
                                        if custom_classes and custom_classes != "all":
                                            # 获取用户选择的类别列表
                                            user_selected_classes = [custom_classes] if isinstance(custom_classes, str) else list(custom_classes)

                                            # 如果模型识别的类别不在用户选择的列表中，则标记为不添加
                                            if class_name not in user_selected_classes:
                                                should_add = False

                                        # 3. 如果标记为添加，则将此检测结果加入列表
                                        if should_add:
                                            detections.append({
                                                'box': [int(x1), int(y1), int(x2), int(y2)],
                                                'confidence': float(confidence),
                                                'class': class_index,
                                                'class_name': class_name  # 使用真实的、未被修改的 class_name
                                            })

                # 记录检测结果
                if detections:
                    # 获取实际检测到的类别名称，但对日志显示进行优化
                    # 确保显示与用户选择的类别一致
                    if custom_classes and custom_classes != "all":
                        # 计算检测到的对象数量
                        detection_count = len(detections)
                        # 使用用户选择的类别名称
                        if isinstance(custom_classes, str):
                            selected_class = custom_classes
                        else:
                            selected_class = custom_classes[0] if custom_classes else "未知"

                        # 简化日志，只显示数量和类别
                        if detection_count > 0:
                            logger.info(f"检测到 {detection_count} 个 {selected_class}")
                    else:
                        # 全部类别模式下简化输出
                        class_counts = {}
                        for d in detections:
                            class_name = d['class_name']
                            class_counts[class_name] = class_counts.get(class_name, 0) + 1

                        # 格式化输出，如 "2个landslide, 1个fallen tree"
                        result_str = ", ".join([f"{count}个{name}" for name, count in class_counts.items()])
                        logger.info(f"检测结果: {result_str}")
                else:
                    logger.info(f"未检测到目标")

                return detections
            except Exception as e:
                logger.error(f"YOLO-World检测失败: {e}")
                return []

class OWLViTDetector(BaseDetector):
    """OWL-ViT开集识别检测器"""
    
    def __init__(self, model_path, confidence_threshold=0.5):
        super().__init__(model_path, confidence_threshold)
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'

    def load_model(self):
        """加载OWL-ViT模型"""
        with self.lock:
            if self.model is None:
                try:
                    try:
                        from transformers import OwlViTProcessor, OwlViTForObjectDetection
                    except ImportError:
                        logger.warning("transformers模块未安装，使用模拟检测器")
                        # 创建模拟处理器和模型
                        class MockProcessor:
                            def __call__(self, text=None, images=None, return_tensors=None):
                                return {}
                                
                            def post_process_object_detection(self, outputs, threshold=0.0, target_sizes=None):
                                return [{"boxes": [], "scores": [], "labels": []}]
                                
                        self.processor = MockProcessor()
                        self.model = lambda **kwargs: {}
                        return
                    
                    logger.info(f"加载OWL-ViT模型到 {self.device}")
                    
                    if os.path.exists(self.model_path):
                        self.processor = OwlViTProcessor.from_pretrained(self.model_path)
                        self.model = OwlViTForObjectDetection.from_pretrained(self.model_path).to(self.device)
                    else:
                        # 使用预训练模型
                        self.processor = OwlViTProcessor.from_pretrained("google/owlvit-base-patch32")
                        self.model = OwlViTForObjectDetection.from_pretrained("google/owlvit-base-patch32").to(self.device)
                except Exception as e:
                    logger.error(f"加载OWL-ViT模型失败: {e}")
                    # 创建模拟处理器和模型，避免系统崩溃
                    class MockProcessor:
                        def __call__(self, text=None, images=None, return_tensors=None):
                            return {}
                            
                        def post_process_object_detection(self, outputs, threshold=0.0, target_sizes=None):
                            return [{"boxes": [], "scores": [], "labels": []}]
                            
                    self.processor = MockProcessor()
                    self.model = lambda **kwargs: {}
    
    def detect(self, image, classes=None):
        """
        使用OWL-ViT执行检测
        Args:
            image: 输入图像
            classes: 类别列表，如果为None则使用默认列表
        Returns:
            检测结果列表
        """
        with self.lock:
            if self.model is None:
                self.load_model()

            try:
                if classes is None:
                    classes = ["landslide", "rockfall", "slope failure", "damaged slope protection"]
                # 将OpenCV图像转换为RGB
                image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                # 处理图像和文本
                inputs = self.processor(text=classes, images=image_rgb, return_tensors="pt").to(self.device)
                # 执行推理
                with torch.no_grad():
                    outputs = self.model(**inputs)
                # 后处理结果
                target_sizes = torch.tensor([image.shape[:2]]).to(self.device)
                results = self.processor.post_process_object_detection(outputs, threshold=self.confidence_threshold, target_sizes=target_sizes)[0]
                
                detections = []
                for box, score, label in zip(results["boxes"], results["scores"], results["labels"]):
                    box = box.cpu().numpy()
                    detections.append({
                        'box': [int(box[0]), int(box[1]), int(box[2]), int(box[3])],
                        'confidence': float(score.cpu().numpy()),
                        'class': int(label.cpu().numpy()),
                        'class_name': classes[int(label.cpu().numpy())]
                    })
                
                return detections
            except Exception as e:
                logger.error(f"OWL-ViT检测失败: {e}")
                return []
class DetectorFactory:
    """检测器工厂，负责创建和管理检测器实例"""
    def __init__(self):
        self.detectors = {}
        self.detector_classes = {
            'yolov8': YOLOv8Detector,
            'yoloworld': YOLOWorldDetector,
            'model1': YOLOv8Detector,    # 模型一对应YOLOv8
            'model2': YOLOWorldDetector,  # 模型二对应YOLOWorld
        }
        self.loading_locks = {}  # 添加锁以防止并发加载同一模型
    def get_detector(self, detector_type, model_path, confidence_threshold=0.5):
        """
        获取检测器实例
        Args:
            detector_type: 检测器类型（yolov8, yoloworld, model1, model2）
            model_path: 模型路径
            confidence_threshold: 置信度阈值
        Returns:
            检测器实例
        """
        detector_key = f"{detector_type}_{model_path}"
        # 如果检测器已存在且已加载，直接返回
        if detector_key in self.detectors:
            return self.detectors[detector_key]
        # 确保模型路径存在
        if not os.path.exists(model_path) and detector_type not in ['yoloworld', 'owlvit']:
            logger.warning(f"模型文件不存在: {model_path}, 使用模拟检测器")
            # 创建一个空文件防止重复警告
            try:
                os.makedirs(os.path.dirname(model_path), exist_ok=True)
                with open(model_path, 'w') as f:
                    f.write('placeholder')
            except Exception as e:
                logger.error(f"创建占位符文件失败: {e}")
        
        # 使用锁防止并发加载同一模型
        if detector_key not in self.loading_locks:
            self.loading_locks[detector_key] = threading.RLock()
        
        with self.loading_locks[detector_key]:
            # 再次检查，可能在等待锁的过程中已经被其他线程加载
            if detector_key in self.detectors:
                return self.detectors[detector_key]
            
            try:
                if detector_type in self.detector_classes:
                    logger.info(f"创建检测器: {detector_type}, 模型路径: {model_path}")
                    detector = self.detector_classes[detector_type](
                        model_path, confidence_threshold
                    )
                    self.detectors[detector_key] = detector
                else:
                    logger.error(f"不支持的检测器类型: {detector_type}, 使用YOLOv8作为后备")
                    detector = YOLOv8Detector(model_path, confidence_threshold)
                    self.detectors[detector_key] = detector
            except Exception as e:
                logger.error(f"创建检测器失败: {e}, 使用模拟检测器")
                # 使用YOLOv8的模拟检测器作为后备
                detector = YOLOv8Detector(model_path, confidence_threshold)
                self.detectors[detector_key] = detector
        
        return self.detectors[detector_key]
    
    def release_all(self):
        """释放所有检测器资源"""
        for detector in self.detectors.values():
            try:
                detector.release()
            except Exception as e:
                logger.error(f"释放检测器资源失败: {e}")
        self.detectors.clear()


class EnsembleDetector:
    """集成检测器，组合多个检测器的结果"""
    def __init__(self, detectors, weights=None):
        """
        初始化集成检测器
        Args:
            detectors: 检测器列表
            weights: 各检测器权重，如果为None则平均权重
        """
        self.detectors = detectors
        
        if weights is None:
            self.weights = [1.0/len(detectors)] * len(detectors)
        else:
            total = sum(weights)
            self.weights = [w/total for w in weights]
    
    def detect(self, image):
        """
        使用所有检测器执行检测并集成结果
        Args:
            image: 输入图像
        Returns:
            集成后的检测结果
        """
        all_detections = []
        # 收集所有检测器的结果
        for i, detector in enumerate(self.detectors):
            try:
                detections = detector.detect(image)
                for det in detections:
                    det['weight'] = self.weights[i]
                all_detections.extend(detections)
            except Exception as e:
                logger.error(f"检测器 {i} 失败: {e}")
        
        # 合并重叠的检测结果
        return self._merge_detections(all_detections)

    def _merge_detections(self, detections, iou_threshold=0.5):
        """
        合并重叠的检测结果
        Args:
            detections: 所有检测结果
            iou_threshold: IoU阈值，用于确定是否合并检测结果
        Returns:
            合并后的检测结果
        """
        if not detections:
            return []
        # 按置信度排序
        detections = sorted(detections, key=lambda x: x['confidence'] * x['weight'], reverse=True)
        merged_detections = []
        
        while detections:
            best = detections.pop(0)
            i = 0
            while i < len(detections):
                if self._calculate_iou(best['box'], detections[i]['box']) > iou_threshold:
                    # 合并检测结果
                    other = detections.pop(i)
                    best = self._merge_detection(best, other)
                else:
                    i += 1
            # 移除权重字段
            if 'weight' in best:
                del best['weight']
                
            merged_detections.append(best)
        
        return merged_detections
    
    def _calculate_iou(self, box1, box2):
        """计算两个边界框的IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2
        
        xi1 = max(x1_1, x1_2)
        yi1 = max(y1_1, y1_2)
        xi2 = min(x2_1, x2_2)
        yi2 = min(y2_1, y2_2)
        
        inter_area = max(xi2 - xi1, 0) * max(yi2 - yi1, 0)
        
        box1_area = (x2_1 - x1_1) * (y2_1 - y1_1)
        box2_area = (x2_2 - x1_2) * (y2_2 - y1_2)
        
        union_area = box1_area + box2_area - inter_area
        
        return inter_area / union_area if union_area > 0 else 0
    
    def _merge_detection(self, det1, det2):
        """
        合并两个检测结果
        采用加权平均合并边界框和置信度
        """
        w1, w2 = det1['weight'], det2['weight']
        total_weight = w1 + w2
        
        # 加权平均边界框
        box = [
            int((det1['box'][i] * w1 + det2['box'][i] * w2) / total_weight)
            for i in range(4)
        ]
        # 加权平均置信度
        confidence = (det1['confidence'] * w1 + det2['confidence'] * w2) / total_weight
        # 保留置信度较高的检测结果的类别
        if det1['confidence'] * w1 >= det2['confidence'] * w2:
            class_id = det1['class']
            class_name = det1['class_name']
        else:
            class_id = det2['class']
            class_name = det2['class_name']
        # 合并权重
        weight = total_weight
        
        return {
            'box': box,
            'confidence': confidence,
            'class': class_id,
            'class_name': class_name,
            'weight': weight
        }

    def process_detections(self, detections):
        """处理检测结果"""
        processed_detections = []
        for det in detections:
            processed_detections.append({
                'class': det['class'],
                'confidence': det['confidence'],
                'bbox': det['box']
            })
        return processed_detections