# 邮件告警冷却策略解决方案

## 问题描述

在边坡检测系统中，当同时检测四个源时，如果多个源同时检测到异常，由于邮件发送的冷却机制，只有第一个检测到的源能够发送邮件，其他源的邮件会被冷却机制阻止。

## 原有机制分析

### 1. 双重冷却机制
- **AlertManager级别冷却**：每个源的告警有30秒冷却期（可配置）
- **EmailProvider级别冷却**：整个邮件提供者有30秒冷却期（原来是60秒）

### 2. 检测流程
- 系统按顺序检测四个源，不是同时检测
- 检测间隔默认为5秒（可在1-60秒范围内调整）
- 当检测到异常时，会触发告警发送

## 三种解决方案

### 方案1：按源独立冷却（推荐）
**策略**：`per_source`
**描述**：移除EmailProvider的全局冷却，只保留按源的冷却
**优点**：
- 每个源独立计算冷却时间
- 四个源同时检测到时，每个源都能发送邮件
- 避免邮件轰炸，每个源仍有冷却保护
- 平衡了及时性和合理性

**配置示例**：
```json
{
  "emailSettings": {
    "cooldownStrategy": "per_source",
    "cooldownSeconds": 30
  }
}
```

### 方案2：缩短冷却时间
**策略**：`per_source` 或 `global`
**描述**：将冷却时间设置为较短的时间（如5-10秒）
**优点**：
- 减少邮件被阻止的概率
- 保持一定的冷却保护
- 提高告警的及时性

**配置示例**：
```json
{
  "emailSettings": {
    "cooldownStrategy": "per_source",
    "cooldownSeconds": 5
  }
}
```

### 方案3：无冷却限制
**策略**：`none`
**描述**：完全移除邮件冷却限制
**优点**：
- 每次检测到异常都发送邮件
- 最大化告警的及时性
- 适合对安全要求极高的场景

**注意**：可能产生大量邮件，需要谨慎使用

**配置示例**：
```json
{
  "emailSettings": {
    "cooldownStrategy": "none",
    "cooldownSeconds": 0
  }
}
```

## 配置方法

### 1. 通过Web界面配置
访问 `http://your-server/alert_config` 页面进行可视化配置

### 2. 通过API配置
```bash
# 设置按源冷却策略
curl -X POST http://your-server/api/alert_config \
  -H "Content-Type: application/json" \
  -d '{
    "email_cooldown_strategy": "per_source",
    "email_cooldown_seconds": 30
  }'

# 设置无冷却策略
curl -X POST http://your-server/api/alert_config \
  -H "Content-Type: application/json" \
  -d '{
    "email_cooldown_strategy": "none",
    "email_cooldown_seconds": 0
  }'
```

### 3. 通过配置文件
编辑 `src/app/data/settings.json` 文件：
```json
{
  "emailSettings": {
    "emailAddresses": "<EMAIL>",
    "smtpServer": "smtp.example.com",
    "smtpPort": 465,
    "smtpSsl": true,
    "senderEmail": "<EMAIL>",
    "senderPassword": "password",
    "cooldownStrategy": "per_source",
    "cooldownSeconds": 30
  },
  "alertSettings": {
    "cooldownSeconds": 30
  }
}
```

## 冷却策略详细说明

### none - 无冷却限制
- 每次检测到异常都立即发送邮件
- 不记录任何冷却时间
- 适合：对安全要求极高，不介意接收大量邮件的场景

### global - 全局冷却
- 所有源共享一个冷却时间
- 任何一个源发送邮件后，所有源都进入冷却期
- 适合：希望控制邮件总量的场景

### per_source - 按源冷却（推荐）
- 每个源独立计算冷却时间
- 源A发送邮件不影响源B的邮件发送
- 适合：大多数实际应用场景

## 监控和状态查看

### 1. 实时状态查看
访问告警配置页面可以看到：
- 当前冷却策略
- 各源的剩余冷却时间
- 告警队列状态
- 邮件提供者状态

### 2. API状态查询
```bash
curl http://your-server/api/alert_config
```

返回示例：
```json
{
  "success": true,
  "alert_manager": {
    "cooldown_seconds": 30,
    "queue_size": 0,
    "is_running": true,
    "providers": ["email"]
  },
  "email_provider": {
    "strategy": "per_source",
    "cooldown_seconds": 30,
    "source_remaining": {
      "camera_1": 15.2,
      "camera_2": 0,
      "camera_3": 0,
      "camera_4": 8.7
    }
  }
}
```

## 推荐配置

### 生产环境推荐
```json
{
  "emailSettings": {
    "cooldownStrategy": "per_source",
    "cooldownSeconds": 30
  },
  "alertSettings": {
    "cooldownSeconds": 30
  }
}
```

### 测试环境推荐
```json
{
  "emailSettings": {
    "cooldownStrategy": "per_source",
    "cooldownSeconds": 5
  },
  "alertSettings": {
    "cooldownSeconds": 5
  }
}
```

### 高安全要求环境
```json
{
  "emailSettings": {
    "cooldownStrategy": "none",
    "cooldownSeconds": 0
  },
  "alertSettings": {
    "cooldownSeconds": 10
  }
}
```

## 注意事项

1. **邮件服务器限制**：某些邮件服务器对发送频率有限制，请根据实际情况调整
2. **存储空间**：无冷却策略可能产生大量邮件，注意邮箱存储空间
3. **网络带宽**：频繁发送邮件会占用网络带宽
4. **告警疲劳**：过多的告警邮件可能导致用户忽视重要告警

## 故障排除

### 1. 邮件发送失败
- 检查SMTP配置是否正确
- 确认网络连接正常
- 查看系统日志获取详细错误信息

### 2. 冷却策略不生效
- 确认配置已保存并重新加载
- 检查邮件提供者是否正确注册
- 重启系统以确保配置生效

### 3. 配置丢失
- 系统会自动备份配置文件
- 可以从 `.bak` 文件恢复配置
- 建议定期备份重要配置
