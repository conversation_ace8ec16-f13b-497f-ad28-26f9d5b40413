import os
import sys
from pathlib import Path


def resource_path(relative_path):
    """ 获取资源的绝对路径，兼容开发环境和 PyInstaller 打包后的环境 """
    if getattr(sys, 'frozen', False):
        # 如果是打包后的 .exe 文件
        base_path = sys._MEIPASS
    else:
        # 如果是正常的 .py 文件
        base_path = os.path.abspath(".")

    # 将'src'从路径中移除，因为打包时我们已经把它作为根了
    # 例如: src/app/models -> app/models
    if relative_path.startswith('src/'):
        relative_path = relative_path[4:]

    return os.path.join(base_path, relative_path)


# 基本配置
DEBUG = True
SECRET_KEY = 'slope-detection-system-secret-key'

# 路径配置
# 注意：我们不再使用 BASE_DIR，而是直接使用 resource_path 函数
STATIC_DIR = resource_path('src/app/static')
UPLOAD_FOLDER = os.path.join(STATIC_DIR, 'uploads')
DETECTION_RESULTS = os.path.join(STATIC_DIR, 'results')

# 确保目录存在 (这在启动时运行，所以路径是正确的)
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(DETECTION_RESULTS, exist_ok=True)

# 视频检测配置
DETECTION_INTERVAL = 5  # 默认检测间隔(秒)
MAX_SOURCES = 4  # 最大同时检测的视频源数量

# 模型配置
MODEL_CONFIGS = {
    'model1': {
        'model_path': resource_path('src/app/models/model1.pt'),
        'description': '边坡监测模型v1'
    },
    'model2': {
        'model_path': resource_path('src/app/models/model2.pt'),
        'description': '边坡监测模型v2'
    },
}

# 图像预处理配置
IMAGE_PREPROCESSING = {
    'night': {
        'enabled': True,
        'description': '增强模式1'
    },
    'rain': {
        'enabled': True,
        'description': '增强模式2'
    },
    'fog': {
        'enabled': True,
        'description': '增强模式3'
    }
}

# 告警配置
ALERT_PHONE_NUMBERS = []  # 预警手机号列表
SMS_API_KEY = ''  # 短信服务API密钥