import time
import threading
import logging
import cv2
import os
import numpy as np
from datetime import datetime
from src.app.models.detector import YOLOWorldDetector

logger = logging.getLogger(__name__)

class DetectionThread:
    """检测线程，定时从视频流中获取帧执行检测"""
    def __init__(self, stream_manager, detector, preprocessor, alert_manager, 
                 app_state, detection_interval=5, save_results=True, results_dir=None):
        """
        初始化检测线程
        Args:
            stream_manager: 视频流管理器
            detector: 检测器
            preprocessor: 图像预处理器
            alert_manager: 告警管理器
            app_state:
            detection_interval: 检测间隔(秒)
            save_results: 是否保存检测结果
            results_dir: 检测结果保存目录
        """
        self.stream_manager = stream_manager
        self.detector = detector
        self.preprocessor = preprocessor
        self.alert_manager = alert_manager
        self.app_state = app_state
        self.detection_interval = detection_interval
        self.save_results = save_results
        self.results_dir = results_dir
        
        self.sources_to_detect = set()  # 要检测的视频流ID集合
        self.is_running = False
        self.lock = threading.RLock()
        self.detect_thread = None
        
        # 检测结果统计
        self.detection_stats = {}
        
        # 确保结果目录存在
        if self.save_results and self.results_dir:
            os.makedirs(self.results_dir, exist_ok=True)
    
    def start(self):
        """启动检测线程"""
        with self.lock:
            if not self.is_running:
                self.is_running = True
                self.detect_thread = threading.Thread(target=self._detection_loop, daemon=True)
                self.detect_thread.start()
                logger.info(f"检测线程启动，检测间隔: {self.detection_interval}秒")
                return True
            return False
    
    def stop(self):
        """停止检测线程"""
        with self.lock:
            if self.is_running:
                self.is_running = False
                if self.detect_thread and self.detect_thread.is_alive():
                    self.detect_thread.join(timeout=self.detection_interval + 1.0)
                logger.info("检测线程停止")
                return True
            return False
    
    def add_source(self, source_id):
        """添加要检测的视频流ID"""
        with self.lock:
            self.sources_to_detect.add(source_id)
            # 初始化该源的统计数据
            if source_id not in self.detection_stats:
                self.detection_stats[source_id] = {
                    'total_detections': 0,
                    'anomalies': 0,
                    'last_detection_time': 0,
                    'last_anomaly_time': 0,
                    'processing_time': 0,
                }
            logger.info(f"添加检测源: {source_id}")
    
    def remove_source(self, source_id):
        """移除要检测的视频流ID"""
        with self.lock:
            if source_id in self.sources_to_detect:
                self.sources_to_detect.remove(source_id)
                logger.info(f"移除检测源: {source_id}")
    
    def get_stats(self, source_id=None):
        """
        获取检测统计数据
        Args:
            source_id: 视频流ID，如果为None则返回所有源的统计数据
        Returns:
            统计数据字典
        """
        with self.lock:
            if source_id is not None:
                return self.detection_stats.get(source_id, {})
            else:
                return self.detection_stats.copy()
    
    def _detection_loop(self):
        """检测循环"""
        while self.is_running:
            try:
                # 获取当前要检测的源列表(复制一份防止并发修改)
                with self.lock:
                    sources = list(self.sources_to_detect)
                
                # 检测每个源
                for source_id in sources:
                    try:
                        # 从流管理器获取最新帧
                        frame, timestamp = self.stream_manager.get_frame(source_id)
                        
                        if frame is None:
                            logger.warning(f"无法获取源 {source_id} 的帧")
                            continue
                        
                        # 执行检测
                        self._detect_frame(source_id, frame, timestamp)
                    
                    except Exception as e:
                        logger.error(f"检测源 {source_id} 时出错: {e}")
                
                # 等待指定的检测间隔
                time.sleep(self.detection_interval)
            
            except Exception as e:
                logger.error(f"检测循环出错: {e}")
                time.sleep(1)  # 出错时短暂停顿
    
    def _detect_frame(self, source_id, frame, timestamp):
        """
        检测单帧
        Args:
            source_id: 视频流ID
            frame: 帧图像
            timestamp: 帧时间戳
        """
        start_time = time.time()
        
        try:
            # 获取视频源对象，检查是否还存在
            source = self.stream_manager.get_source(source_id)
            if not source:
                logger.warning(f"视频源 {source_id} 已不存在，跳过检测")
                return
            
            # 检查是否需要预处理图像
            # 如果视频源级别已经预处理，则不再重复预处理
            if self.preprocessor and not source.preprocess_enabled:
                try:
                    processed_frame = self.preprocessor.preprocess(frame)
                except Exception as e:
                    logger.error(f"预处理帧时出错: {e}")
                    processed_frame = frame
            else:
                # 视频源级别已经预处理过或不需要预处理
                processed_frame = frame
            
            # 执行检测
            try:
                if isinstance(self.detector, YOLOWorldDetector):
                    # 如果是YOLOWorld检测器，传递类别参数
                    selected_class = self.app_state.get('yoloworld_class', 'all')
                    logger.info(f"检测源 {source_id} 使用YOLOWorld检测类别: {selected_class}")
                    detections = self.detector.detect(processed_frame, custom_classes=selected_class)
                else:
                    # 其他检测器，正常调用
                    detections = self.detector.detect(processed_frame)
            except Exception as e:
                logger.error(f"执行检测时出错: {e}")
                detections = []
            
            # 再次检查视频源是否存在，防止在检测过程中被移除
            source = self.stream_manager.get_source(source_id)
            if source:
                # 更新源对象的检测结果
                with source.detection_lock:
                    source.latest_detections = detections
                    # 更新检测时间戳为当前时间
                    source.detection_timestamp = time.time()
                    if detections:
                        logger.debug(f"源 {source_id} 的最新检测结果: {len(detections)} 个目标")
                    else:
                        logger.debug(f"源 {source_id} 未检测到任何目标")
            else:
                logger.warning(f"视频源 {source_id} 在检测过程中被移除")
                # 从检测源列表中移除
                with self.lock:
                    if source_id in self.sources_to_detect:
                        self.sources_to_detect.remove(source_id)
                return
            
            # 更新统计信息
            with self.lock:
                stats = self.detection_stats.get(source_id, {
                    'total_detections': 0,
                    'anomalies': 0,
                    'last_detection_time': 0,
                    'last_anomaly_time': 0,
                    'processing_time': 0,
                })
                
                stats['total_detections'] += 1
                stats['last_detection_time'] = timestamp
                stats['processing_time'] = time.time() - start_time
                
                if detections:
                    stats['anomalies'] += 1
                    stats['last_anomaly_time'] = timestamp
                
                self.detection_stats[source_id] = stats
            
            # 如果有检测结果，保存到文件并发送告警
            if detections:
                # 保存结果图像
                try:
                    # 确保检测结果目录存在
                    if not os.path.exists(self.results_dir):
                        os.makedirs(self.results_dir)
                    
                    # 在图像上绘制检测结果
                    result_frame = processed_frame.copy()
                    for detection in detections:
                        box = detection['box']
                        confidence = detection['confidence']
                        class_name = detection['class_name']
                        
                        x1, y1, x2, y2 = [int(coord) for coord in box]
                        cv2.rectangle(result_frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
                        
                        label = f"{class_name}: {confidence:.2f}"
                        cv2.putText(result_frame, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
                    
                    # 保存到文件
                    filename = f"{source_id}_{int(timestamp)}.jpg"
                    file_path = os.path.join(self.results_dir, filename)
                    cv2.imwrite(file_path, result_frame)
                    
                    # 发送告警
                    if self.alert_manager:
                        alert_message = f"检测到异常：{', '.join([d['class_name'] for d in detections])}"
                        self.alert_manager.send_alert(source_id, "detection", alert_message, file_path)
                except Exception as e:
                    logger.error(f"保存检测结果或发送告警时出错: {e}")
        
        except Exception as e:
            logger.error(f"检测帧时出错: {e}")
            
            # 记录错误到统计数据
            with self.lock:
                if source_id in self.detection_stats:
                    self.detection_stats[source_id]['error'] = str(e)

class DetectionThreadManager:
    """检测线程管理器，用于管理多个检测线程"""
    def __init__(self, max_threads=4):
        self.detection_threads = {}
        self.max_threads = max_threads
        self.lock = threading.RLock()
    
    def create_detection_thread(self, thread_id, stream_manager, detector, preprocessor, 
                               alert_manager, app_state, detection_interval=5, save_results=True,
                               results_dir=None):
        """
        创建并添加检测线程
        Args:
            thread_id: 线程ID
            stream_manager: 视频流管理器
            detector: 检测器
            preprocessor: 图像预处理器
            alert_manager: 告警管理器
            app_state:
            detection_interval: 检测间隔(秒)
            save_results: 是否保存检测结果
            results_dir: 检测结果保存目录
        Returns:
            成功返回True，失败返回False
        """
        with self.lock:
            if len(self.detection_threads) >= self.max_threads:
                logger.error(f"超过最大检测线程数量限制: {self.max_threads}")
                return False
            
            if thread_id in self.detection_threads:
                logger.warning(f"检测线程已存在: {thread_id}")
                return False
            
            # 创建新的检测线程
            thread = DetectionThread(
                stream_manager=stream_manager,
                detector=detector,
                preprocessor=preprocessor,
                alert_manager=alert_manager,
                app_state=app_state,
                detection_interval=detection_interval,
                save_results=save_results,
                results_dir=results_dir
            )
            # 添加到管理器
            self.detection_threads[thread_id] = thread
            # 启动线程
            thread.start()
            
            logger.info(f"创建并启动检测线程: {thread_id}")
            return True
    
    def remove_detection_thread(self, thread_id):
        """
        移除检测线程
        Args:
            thread_id: 线程ID
        Returns:
            成功返回True，失败返回False
        """
        with self.lock:
            if thread_id in self.detection_threads:
                thread = self.detection_threads[thread_id]
                thread.stop()
                del self.detection_threads[thread_id]
                logger.info(f"移除检测线程: {thread_id}")
                return True
            return False
    
    def get_detection_thread(self, thread_id):
        """获取检测线程"""
        with self.lock:
            return self.detection_threads.get(thread_id)
    
    def get_all_threads(self):
        """获取所有检测线程的ID"""
        with self.lock:
            return list(self.detection_threads.keys())
    
    def get_all_stats(self):
        """获取所有线程的统计数据"""
        with self.lock:
            stats = {}
            for thread_id, thread in self.detection_threads.items():
                thread_stats = thread.get_stats()
                stats[thread_id] = thread_stats
            return stats
    
    def start_all(self):
        """启动所有检测线程"""
        with self.lock:
            for thread_id, thread in list(self.detection_threads.items()):
                thread.start()
            logger.info(f"已启动所有检测线程，共 {len(self.detection_threads)} 个")
    
    def stop_all(self):
        """停止所有检测线程"""
        with self.lock:
            for thread_id, thread in list(self.detection_threads.items()):
                thread.stop()
            logger.info(f"已停止所有检测线程，共 {len(self.detection_threads)} 个") 