import os
import time
import logging
import threading
import shutil
import json
from src.app.core import app_core, get_data_dir
from flask import Blueprint, render_template, request, jsonify, Response, send_from_directory
from datetime import datetime
import psutil
from src.app.config.config import MODEL_CONFIGS, UPLOAD_FOLDER, DETECTION_RESULTS

# 创建蓝图
main_bp = Blueprint('main', __name__)

# 持久化与数据安全相关工具函数
def save_json_with_backup(data, filename):
    """保存JSON数据并自动备份"""
    data_dir = get_data_dir()
    filepath = os.path.join(data_dir, filename)
    backup_path = filepath + '.bak'
    try:
        if os.path.exists(filepath):
            shutil.copy(filepath, backup_path)
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.error(f"保存 {filepath} 时出错: {e}")
        raise

def load_json_with_recovery(filename):
    """读取JSON数据，主文件损坏时自动恢复备份"""
    data_dir = get_data_dir()
    filepath = os.path.join(data_dir, filename)
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        backup_path = filepath + '.bak'
        if os.path.exists(backup_path):
            shutil.copy(backup_path, filepath)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logging.error(f"恢复备份 {backup_path} 时出错: {e}")
                return {}
        else:
            logging.error(f"{filepath} 和备份均不可用")
            return {}

@main_bp.route("/")
def index():
    """主页"""
    return render_template("index.html",
                           title='边坡检测系统',
                           active_model=app_core.app_state['current_model'],
                           preprocessing_enabled=app_core.app_state['preprocessing_enabled'],
                           detection_interval=app_core.app_state['detection_interval'],
                           models=MODEL_CONFIGS)



@main_bp.route('/stream/<source_id>')
def video_stream(source_id):
    """视频流路由"""
    return Response(
        app_core.generate_frames(source_id),
        mimetype='multipart/x-mixed-replace; boundary=frame'
    )

@main_bp.route('/api/sources', methods=['GET'])
def get_sources():
    """获取所有视频源"""
    sources = app_core.stream_manager.get_all_sources()
    return jsonify({'sources': sources})

@main_bp.route('/api/sources', methods=['POST'])
def add_source():
    """添加新的视频源"""
    if request.content_type and 'application/json' in request.content_type:
        data = request.json
    else:
        data = request.form
    files = request.files
    result = app_core.add_source(data, files)
    # 持久化数据源并备份
    try:
        sources = app_core.stream_manager.get_all_sources()
        save_json_with_backup(sources, 'sources.json')
    except Exception as e:
        logging.error(f"保存数据源时出错: {e}")
    return result

@main_bp.route('/api/sources/<source_id>', methods=['DELETE'])
def remove_source(source_id):
    """移除视频源"""
    result = app_core.remove_source(source_id)
    # 持久化数据源并备份
    try:
        sources = app_core.stream_manager.get_all_sources()
        save_json_with_backup(sources, 'sources.json')
    except Exception as e:
        logging.error(f"保存数据源时出错: {e}")
    return result

@main_bp.route('/api/status', methods=['GET'])
def get_status():
    """获取系统状态"""
    try:
        active_sources_count = len(app_core.app_state['active_sources'])
        detection_status = app_core.detection_thread_manager.get_all_stats()
        cpu_percent = psutil.cpu_percent()
        memory_info = psutil.virtual_memory()
        memory_percent = memory_info.percent

        return jsonify({
            'success': True,
            'status': {
                'active_sources': active_sources_count,
                'current_model': app_core.app_state['current_model'],
                'detection_interval': app_core.app_state['detection_interval'],
                'preprocessing_enabled': app_core.app_state['preprocessing_enabled'],
                'yoloworld_class': app_core.app_state.get('yoloworld_class', 'all'),
                'system': {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory_percent
                },
                'detection_threads': detection_status
            }
        })
    except Exception as e:
        logging.error(f"获取系统状态失败: {e}")
        return jsonify({'error': f'获取系统状态失败: {str(e)}'}), 500

@main_bp.route('/api/model', methods=['POST'])
def set_model():
    """设置当前使用的模型"""
    try:
        data = request.json
        model_type = data.get('model')

        if model_type not in MODEL_CONFIGS:
            return jsonify({'error': f'不支持的模型类型: {model_type}'}), 400

        app_core.app_state['current_model'] = model_type

        threads = app_core.detection_thread_manager.get_all_threads()
        for thread_id in threads:
            thread = app_core.detection_thread_manager.get_detection_thread(thread_id)
            if thread:
                thread.detector = app_core.get_current_detector()

        return jsonify({'success': True, 'current_model': model_type})

    except Exception as e:
        logging.error(f"设置模型时出错: {e}")
        return jsonify({'error': f'设置模型失败: {str(e)}'}), 500

@main_bp.route('/api/preprocessing', methods=['POST'])
def set_preprocessing():
    """启用/禁用图像预处理"""
    try:
        data = request.json
        enabled = data.get('enabled', True)

        previous_state = app_core.app_state['preprocessing_enabled']
        app_core.app_state['preprocessing_enabled'] = enabled

        for source_id in app_core.app_state['active_sources']:
            source = app_core.stream_manager.get_source(source_id)
            if source:
                source.set_preprocessor(
                    app_core.preprocessor if enabled else None,
                    enabled=enabled
                )

                if previous_state != enabled and source.is_running:
                    def restart_source(src):
                        try:
                            src.stop()
                            time.sleep(0.5)
                            src.start()
                            src.set_preprocessor(
                                app_core.preprocessor if enabled else None,
                                enabled=enabled
                            )
                        except Exception as e:
                            logging.error(f"重启数据源失败: {e}")

                    threading.Thread(
                        target=restart_source,
                        args=(source,),
                        daemon=True
                    ).start()

        threads = app_core.detection_thread_manager.get_all_threads()
        for thread_id in threads:
            thread = app_core.detection_thread_manager.get_detection_thread(thread_id)
            if thread:
                thread.preprocessor = app_core.preprocessor if enabled else None

        return jsonify({'success': True, 'preprocessing_enabled': enabled})

    except Exception as e:
        logging.error(f"设置图像预处理时出错: {e}")
        return jsonify({'error': f'设置图像预处理失败: {str(e)}'}), 500

@main_bp.route('/api/detection_interval', methods=['POST'])
def set_detection_interval():
    """设置检测间隔"""
    try:
        data = request.json
        interval = data.get('interval')

        if interval is None or not (1 <= interval <= 60):
            return jsonify({'error': '无效的检测间隔，必须在1-60秒范围内'}), 400

        app_core.app_state['detection_interval'] = interval

        threads = app_core.detection_thread_manager.get_all_threads()
        for thread_id in threads:
            thread = app_core.detection_thread_manager.get_detection_thread(thread_id)
            if thread:
                thread.detection_interval = interval

        return jsonify({'success': True, 'detection_interval': interval})

    except Exception as e:
        logging.error(f"设置检测间隔时出错: {e}")
        return jsonify({'error': f'设置检测间隔失败: {str(e)}'}), 500

@main_bp.route('/api/alerts', methods=['GET'])
def get_alerts():
    """获取告警历史"""
    try:
        alerts = []

        for filename in os.listdir(DETECTION_RESULTS):
            if filename.endswith('.jpg'):
                file_path = os.path.join(DETECTION_RESULTS, filename)
                file_stat = os.stat(file_path)

                parts = filename.split('_')
                if len(parts) > 1:
                    try:
                        timestamp = int(parts[-1].replace('.jpg', ''))
                        source_id = '_'.join(parts[:-1])

                        alerts.append({
                            'source_id': source_id,
                            'timestamp': timestamp,
                            'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                            'image_url': f'/results/{filename}'
                        })
                    except:
                        pass

        alerts.sort(key=lambda x: x['timestamp'], reverse=True)
        return jsonify({'alerts': alerts})

    except Exception as e:
        logging.error(f"获取告警记录时出错: {e}")
        return jsonify({'error': f'获取告警记录失败: {str(e)}'}), 500

@main_bp.route('/results/<filename>')
def get_result_image(filename):
    """获取检测结果图像"""
    return send_from_directory(DETECTION_RESULTS, filename)

@main_bp.route('/uploads/<filename>')
def get_upload_file(filename):
    """获取上传的文件"""
    return send_from_directory(UPLOAD_FOLDER, filename)

@main_bp.route('/api/settings', methods=['GET'])
def get_settings():
    """获取系统设置（带自动恢复机制）"""
    try:
        settings = load_json_with_recovery('settings.json')
        return jsonify({
            'success': True,
            'settings': settings
        })
    except Exception as e:
        logging.error(f"获取设置时出错: {e}")
        return jsonify({'error': f'获取设置失败: {str(e)}'}), 500

@main_bp.route('/api/settings', methods=['POST'])
def save_settings():
    """保存系统设置（自动备份）"""
    try:
        data = request.get_json()
        if not isinstance(data, dict):
            return jsonify({'error': '无效的请求，必须为JSON对象'}), 400
        save_json_with_backup(data, 'settings.json')
        app_core.app_state['settings'] = data
        app_core._register_alert_providers()  # 保存后重新注册告警
        return jsonify({'success': True}), 200
    except Exception as e:
        logging.error(f"处理 /api/settings 请求时出错: {e}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500

@main_bp.route('/favicon.ico')
def favicon():
    """提供网站图标"""
    return send_from_directory(
        os.path.join(main_bp.root_path, 'static', 'images'),
        'favicon.ico',
        mimetype='image/vnd.microsoft.icon'
    )

@main_bp.route('/api/yoloworld_class', methods=['POST'])
def set_yoloworld_class():
    """设置YOLO-World的检测类别"""
    try:
        data = request.json
        class_name = data.get('class', 'all')
        app_core.app_state['yoloworld_class'] = class_name
        return jsonify({'success': True, 'class': class_name})
    except Exception as e:
        logging.error(f"设置YOLOWorld检测类别失败: {e}")
        return jsonify({'error': f'设置失败: {str(e)}'}), 500

