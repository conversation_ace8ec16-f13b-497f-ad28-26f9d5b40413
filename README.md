# 边坡检测系统

本系统用于道路边坡、护坡等地质灾害（如滑坡、落石、塌方等）的智能监测与告警，支持多种视频源和检测模型，具备实时预警、图像增强、状态持久化和友好的 Web 管理界面。

---

## 主要功能

- **多模型支持**  
  - 支持 YOLOv8、YOLO-World 等主流目标检测模型，适配不同场景。
  - YOLO-World 支持开放词汇检测，可通过文本提示检测任意类别，并支持类别选择（如 fallen tree、landslide、road collapse、stone）。
  - 实时切换模型，无需重启服务。

- **多视频源支持**  
  - 支持摄像头、视频文件、图片文件、网络视频流（RTSP/RTMP/HTTP）。
  - 可动态添加、移除视频源，支持高延迟流自动重连。

- **智能图像预处理**  
  - 自动检测夜间、雨天、大雾等天气条件，智能增强图像质量。
  - 预处理功能可独立开关，支持夜间、雨天、大雾增强效果。
  - 仅在达到严重程度时自动处理，避免资源浪费。

- **实时流展示与告警通知**  
  - Web 界面实时显示处理后的视频流和检测结果（含检测框和置信度）。
  - 支持日志、短信、邮件等多种告警方式，冷却机制防止重复告警。
  - 告警历史可分页浏览，支持截图回溯。

- **多线程高性能处理**  
  - 视频流和检测任务多线程并发，提升系统响应速度。

- **状态持久化与安全**  
  - 视频源和系统设置自动保存、备份，异常时自动恢复，防止数据丢失。
  - 所有文件操作均有异常处理，提升系统健壮性。

- **友好 Web 管理界面**  
  - 支持模型切换、参数配置、告警设置、视频源管理、告警历史查询等操作。

---

## 安装与部署

### 环境要求

- Python 3.8 及以上
- 推荐具备 CUDA 支持的 GPU（用于模型加速）

### 安装步骤

1. **克隆项目代码**
    ```bash
    git clone <项目Git地址>
    cd <项目目录>
    ```

2. **创建虚拟环境（推荐）**
    ```bash
    # Windows
    python -m venv venv
    venv\Scripts\activate

    # Linux/Mac
    python -m venv venv
    source venv/bin/activate
    ```

3. **安装依赖**
    ```bash
    pip install -r requirements.txt
    ```
    *如需 YOLO-World，请根据官方文档安装 yoloworld 库。transformers 已包含在 requirements.txt。*

4. **准备模型文件**
    - 将 `.pt` 等模型权重文件放入 `src/app/models` 目录。
    - 配置文件 `src/app/config/config.py` 中可定义模型名称和路径。

---

## 使用方法

### 启动系统

```bash
python run.py
```
系统默认运行在 `http://localhost:5000`，浏览器访问即可进入管理界面。

### Web界面说明

- **导航栏**：首页、设置、告警历史入口。
- **左侧控制面板**：
    - 检测模型选择
    - 图像预处理开关及参数
    - 检测间隔设置
    - 添加视频源按钮
    - 最近告警信息
- **右侧监控视图**：实时显示所有视频源画面及检测结果。
- **模态框**：
    - 添加视频源（摄像头、视频、图片、网络流）
    - 系统设置（告警方式、预处理参数等）
    - 告警历史（分页浏览所有告警记录和截图）

### 操作流程

1. 添加视频源，选择类型并填写相关信息。
2. 选择检测模型，配置检测参数和预处理选项。
3. 设置告警方式（短信、邮件等）。
4. 实时查看检测结果和告警历史。

---

## 配置说明

- **主要配置文件**：`src/app/config/config.py`
    - `MODEL_CONFIGS`：模型名称与路径
    - `DETECTION_INTERVAL`：检测间隔
    - `MAX_SOURCES`：最大视频源数
    - `UPLOAD_FOLDER`、`DETECTION_RESULTS`：上传和结果存储路径
- **持久化数据**：
    - 视频源信息：`src/app/data/sources.json`
    - 系统设置：`src/app/data/settings.json`
    - 自动备份与恢复机制，防止数据丢失

---

## 代码结构

- `run.py`：项目入口，启动 Flask 服务，支持热重载和自动打开浏览器
- `src/app/__init__.py`：初始化 Flask 应用
- `src/app/routes.py`：Web 路由与 API，含持久化与数据安全机制
- `src/app/core.py`：系统核心逻辑，管理视频源、模型、告警等
- `src/app/config/config.py`：系统配置项
- `src/app/models/detector.py`：检测器基类与模型实现
- `src/app/utils/video_stream.py`：视频流管理
- `src/app/utils/image_preprocessing.py`：图像预处理
- `src/app/utils/detector_thread.py`：检测线程管理
- `src/app/utils/alert.py`：告警管理与多种告警方式
- `src/app/utils/drawing.py`：检测结果绘制
- `src/app/static/`：前端静态资源（CSS、JS、图片、上传、结果）
- `src/app/templates/`：HTML模板
- `src/app/data/`：持久化数据（sources.json、settings.json）

---

## 测试与开发建议

- 推荐使用 `pytest` 补充单元测试，覆盖核心 API 和工具函数。
- 代码风格遵循 PEP8，建议使用 IDE 自动格式化。
- 开发时可设置 `debug_mode=True`，支持热重载和详细错误提示。

---

## 其他说明

- 支持多种告警方式，需在设置中配置相关参数（如手机号、邮箱、SMTP等）。
- 所有配置和数据均自动备份，异常时可自动恢复。
- 支持高并发和多源实时监控，适合实际工程部署。

---