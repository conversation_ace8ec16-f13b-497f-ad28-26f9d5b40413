from flask import Flask
from .core import app_core
from .routes import main_bp
from .config.config import resource_path

def create_app():

    template_dir = resource_path('src/app/templates')
    static_dir = resource_path('src/app/static')

    # 在创建Flask实例时，明确指定这两个文件夹的路径
    app = Flask(__name__,
                template_folder=template_dir,
                static_folder=static_dir)

    # 注册蓝图
    app.register_blueprint(main_bp)

    # 初始化应用核心
    with app.app_context():
        app_core.init_app()

    return app