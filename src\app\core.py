import os
import sys
import time
import logging
import json
import cv2
import threading
import numpy as np

from urllib.parse import unquote
from werkzeug.utils import secure_filename
from src.app.config.config import MODEL_CONFIGS, DETECTION_INTERVAL, MAX_SOURCES, UPLOAD_FOLDER, DETECTION_RESULTS
from src.app.utils.drawing import draw_detections, draw_status_info
from src.app.utils.image_preprocessing import CustomImagePreprocessor
from src.app.models.detector import DetectorFactory
from src.app.utils.video_stream import StreamManager
from src.app.utils.alert import AlertManager, LogAlertProvider, EmailAlertProvider
from src.app.utils.detector_thread import DetectionThreadManager


def get_data_dir():
    """获取数据目录，兼容 exe 和开发环境"""
    if getattr(sys, 'frozen', False):
        # exe 运行时
        base_dir = os.path.dirname(sys.executable)
        data_dir = os.path.join(base_dir, 'app', 'data')
    else:
        # 脚本运行时
        base_dir = os.path.abspath(os.path.dirname(__file__))
        data_dir = os.path.join(base_dir, 'data')
    os.makedirs(data_dir, exist_ok=True)
    return data_dir


class AppCore:
    def __init__(self):
        self.logger = logging.getLogger(__name__)

        self.UPLOAD_FOLDER = UPLOAD_FOLDER
        self.MODEL_CONFIGS = MODEL_CONFIGS
        self.DETECTION_INTERVAL = DETECTION_INTERVAL
        self.MAX_SOURCES = MAX_SOURCES
        self.DETECTION_RESULTS = DETECTION_RESULTS

        # 为打包后的程序创建正确的数据文件路径
        data_dir = get_data_dir()
        self.SOURCES_FILE = os.path.join(data_dir, 'sources.json')
        self.SETTINGS_FILE = os.path.join(data_dir, 'settings.json')
        # --- 路径重大修改结束 ---

        # 全局对象
        self.detector_factory = DetectorFactory()
        self.stream_manager = StreamManager(max_sources=MAX_SOURCES)
        self.alert_manager = AlertManager(cooldown_seconds=30)
        self.preprocessor = CustomImagePreprocessor()
        self.detection_thread_manager = DetectionThreadManager(max_threads=MAX_SOURCES)

        # 全局状态
        self.app_state = {
            'current_model': 'model1',  # 默认使用模型一
            'preprocessing_enabled': False,  # 默认禁用图像预处理
            'detection_interval': DETECTION_INTERVAL,  # 默认检测间隔
            'active_sources': {},  # 活动视频源
            'detection_results': {},  # 检测结果
            'settings': {
                'emailSettings': {
                    'emailAddresses': '',  # 接收邮件的地址
                    'smtpServer': '',  # SMTP服务器
                    'smtpPort': 465,  # SMTP端口
                    'smtpSsl': True,  # 是否使用SSL
                    'senderEmail': '',  # 发件人邮箱
                    'senderPassword': ''  # 发件人密码或授权码
                },
                'nightMode': False,  # 夜间增强
                'rainMode': False,  # 雨天增强
                'fogMode': False,  # 大雾增强
                'yoloworld_class': 'all'  # 默认检测所有类别
            },
            'yoloworld_class': 'all'  # 默认检测所有类别
        }

        # 线程锁
        self.frame_locks = {}

        # 确保目录存在
        os.makedirs(UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(DETECTION_RESULTS, exist_ok=True)



    def save_settings(self, data):
        """
        保存系统设置。
        返回 (结果字典, HTTP状态码)。
        """
        try:
            self.logger.info(f"收到设置保存请求: {data}")

            # 跟踪环境设置是否有变化，以便决定是否需要重启视频源
            env_settings_changed = False
            old_env_settings = {
                'nightMode': self.app_state['settings'].get('nightMode', False),
                'rainMode': self.app_state['settings'].get('rainMode', False),
                'fogMode': self.app_state['settings'].get('fogMode', False)
            }

            if 'emailSettings' in data:
                # 做更详细的更新，防止覆盖
                if 'emailAddresses' in data['emailSettings']: self.app_state['settings']['emailSettings'][
                    'emailAddresses'] = data['emailSettings']['emailAddresses']
                if 'smtpServer' in data['emailSettings']: self.app_state['settings']['emailSettings']['smtpServer'] = \
                    data['emailSettings']['smtpServer']
                if 'smtpPort' in data['emailSettings']: self.app_state['settings']['emailSettings']['smtpPort'] = int(
                    data['emailSettings']['smtpPort'])
                if 'smtpSsl' in data['emailSettings']: self.app_state['settings']['emailSettings']['smtpSsl'] = \
                    data['emailSettings']['smtpSsl']
                if 'senderEmail' in data['emailSettings']: self.app_state['settings']['emailSettings']['senderEmail'] = \
                    data['emailSettings']['senderEmail']
                if 'senderPassword' in data['emailSettings']: self.app_state['settings']['emailSettings'][
                    'senderPassword'] = data['emailSettings']['senderPassword']

            if 'nightMode' in data:
                new_val = data['nightMode']
                if self.app_state['settings'].get('nightMode') != new_val:
                    self.app_state['settings']['nightMode'] = new_val
                    env_settings_changed = True

            if 'rainMode' in data:
                new_val = data['rainMode']
                if self.app_state['settings'].get('rainMode') != new_val:
                    self.app_state['settings']['rainMode'] = new_val
                    env_settings_changed = True

            if 'fogMode' in data:
                new_val = data['fogMode']
                if self.app_state['settings'].get('fogMode') != new_val:
                    self.app_state['settings']['fogMode'] = new_val
                    env_settings_changed = True

            # 根据新设置重新注册告警提供者
            self._register_alert_providers()

            # 如果环境设置有变化且预处理功能已启用，则需要更新并重启相关视频源
            if env_settings_changed and self.app_state['preprocessing_enabled']:
                self.logger.info("环境设置已更改，将更新预处理器并重启相关数据源。")
                self.preprocessor.update_settings(self.app_state['settings'])
                for source_id in list(self.app_state['active_sources'].keys()):
                    source = self.stream_manager.get_source(source_id)
                    if source and source.is_running() and source.is_preprocess_enabled():
                        def restart_source_thread(src):
                            try:
                                self.logger.info(f"正在重启数据源 {src.get_id()} 以应用新的环境设置...")
                                src.stop()
                                time.sleep(0.5)
                                src.start()
                                src.set_preprocessor(self.preprocessor, enabled=True)
                                self.logger.info(f"数据源 {src.get_id()} 重启成功。")
                            except Exception as e:
                                self.logger.error(f"重启数据源 {src.get_id()} 时出错: {e}")

                        threading.Thread(target=restart_source_thread, args=(source,), daemon=True).start()

            # 将最终设置保存到文件
            self.save_settings_to_file()

            return {'success': True, 'settings': self.app_state['settings']}, 200

        except Exception as e:
            self.logger.error(f"保存设置时出错: {e}", exc_info=True)
            return {'error': f'保存设置失败: {str(e)}'}, 500

    def init_app(self):
        """初始化应用核心"""
        self.load_settings_from_file()
        self.load_sources()
        self.preprocessor.update_settings(self.app_state['settings'])
        self._register_alert_providers()

        # 初始化告警提供者
        self.alert_manager.register_provider('log', LogAlertProvider())
        self.alert_manager.start()

    def get_current_detector(self):
        """获取当前配置的检测器"""
        model_type = self.app_state['current_model']
        model_config = MODEL_CONFIGS.get(model_type)
        if not model_config:
            self.logger.error(f"无效的模型类型: {model_type}")
            return None

        model_path = model_config['model_path']
        return self.detector_factory.get_detector(model_type, model_path)

    def save_sources(self):
        """保存视频源到文件"""
        try:
            with open(self.SOURCES_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.app_state['active_sources'], f, ensure_ascii=False, indent=2)
            self.logger.info("数据源信息已保存到文件")
        except Exception as e:
            self.logger.error(f"保存数据源信息失败: {e}")

    def load_sources(self):
        """从文件加载视频源"""
        if not os.path.exists(self.SOURCES_FILE):
            self.logger.debug("没有找到已保存的数据源信息")
            return

        # 恢复视频源前先获取当前检测器
        try:
            with open(self.SOURCES_FILE, 'r') as f:
                saved_sources = json.load(f)
                self.logger.info(f"从文件加载了 {len(saved_sources)} 个数据源信息")

            current_detector = self.get_current_detector()

            # 恢复视频源
            for source_id, source_info in saved_sources.items():
                try:
                    source_type = source_info.get('type')
                    name = source_info.get('name')

                    # 跳过摄像头类型的源，不自动加载它们
                    if source_type == 'webcam':
                        self.logger.info(f"跳过摄像头源: {name}")
                        # 保留源信息但不实际加载
                        self.app_state['active_sources'][source_id] = source_info
                        continue

                    elif source_type == 'video':
                        path = source_info.get('path')
                        if path and os.path.exists(path):
                            loop = source_info.get('loop', False)

                            # 添加视频完成的回调函数，采用更安全的方式移除源
                            def on_video_complete(completed_source_id):
                                self.logger.info(f"视频 {completed_source_id} 播放完毕")
                                try:
                                    # 只处理状态变更，不立即移除源
                                    if completed_source_id in self.app_state['active_sources']:
                                        self.app_state['active_sources'][completed_source_id]['completed'] = True

                                    # 延迟5秒再移除源，避免影响其他操作
                                    def delayed_remove():
                                        time.sleep(5)
                                        self.logger.info(f"延迟移除数据源: {completed_source_id}")
                                        self.remove_source_internal(completed_source_id)

                                    # 使用后台线程延迟移除
                                    threading.Thread(
                                        target=delayed_remove,
                                        daemon=True
                                    ).start()
                                except Exception as e:
                                    self.logger.error(f"处理数据完成回调时出错: {e}")

                            success, new_source_id = self.stream_manager.create_and_add_video(
                                video_path=path,
                                name=name,
                                loop=loop,
                                on_complete=None if loop else on_video_complete
                            )

                            if success:
                                self.logger.info(f"成功恢复数据: {name}")
                                # 更新active_sources中的信息
                                self.app_state['active_sources'][new_source_id] = source_info
                                # 设置预处理器
                                source = self.stream_manager.get_source(new_source_id)
                                if source and self.app_state['preprocessing_enabled']:
                                    # 确保预处理器使用当前环境设置
                                    self.preprocessor.update_settings(self.app_state['settings'])
                                    source.set_preprocessor(self.preprocessor, enabled=True)


                    # 仅当检测器可用时，才为恢复的源创建检测线程
                    if source_id in self.app_state['active_sources'] and current_detector and source_type != 'webcam':
                        # 创建或获取线程
                        thread_id = source_info.get('thread_id',
                                                    f"thread_{len(self.detection_thread_manager.get_all_threads()) + 1}")
                        thread = self.detection_thread_manager.get_detection_thread(thread_id)

                        if not thread:
                            # 创建新线程，使用当前获取的检测器
                            self.detection_thread_manager.create_detection_thread(
                                thread_id=thread_id,
                                stream_manager=self.stream_manager,
                                detector=current_detector,
                                preprocessor=self.preprocessor if self.app_state['preprocessing_enabled'] else None,
                                alert_manager=self.alert_manager,
                                app_state=self.app_state,
                                detection_interval=self.app_state['detection_interval'],
                                results_dir=DETECTION_RESULTS
                            )
                            thread = self.detection_thread_manager.get_detection_thread(thread_id)
                        # 将源添加到线程
                        if thread:
                            thread.add_source(source_id)
                            # 更新线程ID
                            self.app_state['active_sources'][source_id]['thread_id'] = thread_id

                except Exception as e:
                    self.logger.error(f"恢复数据源 {source_id} 失败: {e}")

        except Exception as e:
            self.logger.error(f"加载数据源信息失败: {e}")

    def save_settings_to_file(self):
        """保存系统设置到文件"""
        try:
            with open(self.SETTINGS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.app_state['settings'], f, ensure_ascii=False, indent=2)
            self.logger.info("系统设置已保存到文件")
        except Exception as e:
            self.logger.error(f"保存系统设置失败: {e}")

    def load_settings_from_file(self):
        """从文件加载系统设置"""
        if not os.path.exists(self.SETTINGS_FILE):
            self.logger.info("没有找到已保存的系统设置")
            return

        try:
            with open(self.SETTINGS_FILE, 'r', encoding='utf-8') as f:
                saved_settings = json.load(f)
            # 更新app_state中的设置
            self.app_state['settings'].update(saved_settings)
            self.logger.info("从文件加载了系统设置")
            # 更新预处理器设置
            self.preprocessor.update_settings(self.app_state['settings'])
            # 注册告警提供者
            self._register_alert_providers()

        except Exception as e:
            self.logger.error(f"加载系统设置失败: {e}")

    def _register_alert_providers(self):
        """注册告警提供者"""
        try:

            # 注册邮件告警提供者
            email_settings = self.app_state['settings'].get('emailSettings', {})
            email_addresses = email_settings.get('emailAddresses', '')
            smtp_server = email_settings.get('smtpServer', '')
            smtp_port = email_settings.get('smtpPort', 465)
            smtp_ssl = email_settings.get('smtpSsl', True)
            sender_email = email_settings.get('senderEmail', '')
            sender_password = email_settings.get('senderPassword', '')

            # 如果邮件设置都已配置，则注册邮件告警提供者
            self.logger.info(f"邮件告警配置: smtp_server={smtp_server}, smtp_port={smtp_port}, sender_email={sender_email}, recipient_emails={email_addresses}, use_ssl={smtp_ssl}")
            if email_addresses and smtp_server and sender_email and sender_password:
                self.alert_manager.unregister_provider('email')
                email_provider = EmailAlertProvider(
                    smtp_server=smtp_server,
                    smtp_port=smtp_port,
                    sender_email=sender_email,
                    sender_password=sender_password,
                    recipient_emails=email_addresses.split(','),
                    use_ssl=smtp_ssl
                )
                self.alert_manager.register_provider('email', email_provider)
                self.logger.info(f"已注册邮件告警提供者，收件人: {email_addresses}，使用按源冷却策略")
            else:
                self.logger.warning("邮件告警配置不完整，未注册邮件告警提供者")

        except Exception as e:
            self.logger.error(f"注册告警提供者时出错: {e}")

    def generate_frames(self, source_id):
        """生成视频帧"""
        decoded_source_id = unquote(source_id)

        if decoded_source_id not in self.frame_locks:
            self.frame_locks[decoded_source_id] = threading.Lock()

        no_frame_count = 0
        max_no_frame_attempts = 50
        frame_interval = 0.05  # ~20 FPS

        while True:
            try:
                source_info = self.app_state['active_sources'].get(decoded_source_id, {})
                video_completed = source_info.get('completed', False)

                if video_completed and source_info.get('type') == 'video' and not source_info.get('loop', False):
                    end_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                    video_name = source_info.get('name', 'Unknown video')
                    cv2.putText(end_frame, f"{video_name}", (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255),
                                2)
                    cv2.putText(end_frame, "Playback completed", (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.8,
                                (255, 255, 255), 2)
                    _, buffer = cv2.imencode('.jpg', end_frame)
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                    time.sleep(1.0)
                    continue

                with self.frame_locks[decoded_source_id]:
                    frame, timestamp = self.stream_manager.get_frame(decoded_source_id)

                if frame is None:
                    no_frame_count += 1
                    if no_frame_count > max_no_frame_attempts:
                        self.logger.warning(f"无法获取 {decoded_source_id} 的数据帧。")
                        # Break the loop or show an error frame if the stream is dead
                        error_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                        cv2.putText(error_frame, "Stream unavailable", (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.7,
                                    (0, 0, 255), 2)
                        _, buffer = cv2.imencode('.jpg', error_frame)
                        frame_bytes = buffer.tobytes()
                        yield (b'--frame\r\n'
                               b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                        time.sleep(1.0)
                    else:
                        time.sleep(0.1)
                    continue

                no_frame_count = 0

                source = self.stream_manager.get_source(decoded_source_id)
                detections, detection_age = None, None
                if source:
                    with source.detection_lock:
                        current_time = time.time()
                        result_age = current_time - source.detection_timestamp
                        if source.latest_detections and result_age <= source.detection_valid_period:
                            detections = source.latest_detections
                            detection_age = result_age

                if detections:
                    frame = draw_detections(frame, detections)

                frame = draw_status_info(frame, source_info, detection_age)

                _, buffer = cv2.imencode('.jpg', frame)
                frame_bytes = buffer.tobytes()

                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

                time.sleep(frame_interval)

            except Exception as e:
                self.logger.error(f"生成数据流时出错 {decoded_source_id}: {e}")
                # Yield an error frame and then break to stop the stream for this client
                error_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(error_frame, "Stream Error", (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                _, buffer = cv2.imencode('.jpg', error_frame)
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                break

    def add_source(self, data, files=None):
        """添加视频源"""
        source_type = data.get('type')
        self.logger.info(f"添加数据源请求，类型: {source_type}")

        try:
            if source_type == 'webcam':
                camera_id = data.get('camera_id')
                if camera_id is None:
                    return {'error': '缺少摄像头ID'}, 400

                camera_id = int(camera_id)
                name = data.get('name', f'摄像头_{camera_id}')
                width = int(data.get('width', 640))
                height = int(data.get('height', 480))
                fps = int(data.get('fps', 30))

                webcam_id = f"webcam_{camera_id}"
                if webcam_id in self.app_state['active_sources']:
                    source = self.stream_manager.get_source(webcam_id)
                    if source and source.is_running():
                        return {'success': True, 'source_id': webcam_id, 'message': 'Source already exists'}, 200

                success, source_id = self.stream_manager.create_and_add_webcam(
                    camera_id=camera_id, name=name, width=width, height=height, fps=fps)

                if success:
                    source = self.stream_manager.get_source(source_id)
                    if source and self.app_state['preprocessing_enabled']:
                        self.preprocessor.update_settings(self.app_state['settings'])
                        source.set_preprocessor(self.preprocessor, enabled=True)

                    thread_id = f"thread_{len(self.detection_thread_manager.get_all_threads()) + 1}"
                    detector = self.get_current_detector()
                    self.detection_thread_manager.create_detection_thread(
                        thread_id=thread_id, stream_manager=self.stream_manager, detector=detector,
                        preprocessor=self.preprocessor if self.app_state['preprocessing_enabled'] else None,
                        alert_manager=self.alert_manager, app_state=self.app_state,
                        detection_interval=self.app_state['detection_interval'],
                        results_dir=self.DETECTION_RESULTS)

                    thread = self.detection_thread_manager.get_detection_thread(thread_id)
                    if thread:
                        thread.add_source(source_id)

                    self.app_state['active_sources'][source_id] = {'type': 'webcam', 'name': name,
                                                                   'thread_id': thread_id}
                    self.save_sources()
                    return {'success': True, 'source_id': source_id}, 200
                else:
                    return {'error': '无法添加摄像头'}, 500

            elif source_type == 'video':
                video_file = files.get('video')
                if not video_file:
                    return {'error': '未找到视频文件'}, 400

                filename = secure_filename(video_file.filename)
                file_path = os.path.join(self.UPLOAD_FOLDER, filename)
                video_file.save(file_path)

                name = data.get('name', filename)
                loop = data.get('loop', '') in ['true', 'True', '1', 'on', True]

                def on_video_complete(completed_source_id):
                    self.logger.info(f"视频 {completed_source_id} 播放完毕")
                    if completed_source_id in self.app_state['active_sources']:
                        self.app_state['active_sources'][completed_source_id]['completed'] = True

                    def delayed_remove():
                        time.sleep(5)
                        self.remove_source_internal(completed_source_id)

                    threading.Thread(target=delayed_remove, daemon=True).start()

                success, source_id = self.stream_manager.create_and_add_video(
                    video_path=file_path, name=name, loop=loop, on_complete=None if loop else on_video_complete)

                if success:
                    source = self.stream_manager.get_source(source_id)
                    if source and self.app_state['preprocessing_enabled']:
                        self.preprocessor.update_settings(self.app_state['settings'])
                        source.set_preprocessor(self.preprocessor, enabled=True)

                    thread_id = f"thread_{len(self.detection_thread_manager.get_all_threads()) + 1}"
                    detector = self.get_current_detector()
                    self.detection_thread_manager.create_detection_thread(
                        thread_id=thread_id, stream_manager=self.stream_manager, detector=detector,
                        preprocessor=self.preprocessor if self.app_state['preprocessing_enabled'] else None,
                        alert_manager=self.alert_manager, app_state=self.app_state,
                        detection_interval=self.app_state['detection_interval'],
                        results_dir=self.DETECTION_RESULTS)

                    thread = self.detection_thread_manager.get_detection_thread(thread_id)
                    if thread:
                        thread.add_source(source_id)

                    self.app_state['active_sources'][source_id] = {
                        'type': 'video', 'name': name, 'path': file_path, 'loop': loop,
                        'thread_id': thread_id, 'completed': False
                    }
                    self.save_sources()
                    return {'success': True, 'source_id': source_id}, 200
                else:
                    return {'error': '无法添加视频文件'}, 500


            elif source_type == 'image':
                image_file = files.get('image')
                if not image_file:
                    return {'error': '未找到图像文件'}, 400

                filename = secure_filename(image_file.filename)
                file_path = os.path.join(self.UPLOAD_FOLDER, filename)
                image_file.save(file_path)
                name = data.get('name', filename)
                success, source_id = self.stream_manager.create_and_add_image(image_path=file_path, name=name)

                if success:
                    # --- 这是需要补全的关键逻辑 ---
                    source = self.stream_manager.get_source(source_id)
                    # 图片也应用预处理设置
                    if source and self.app_state['preprocessing_enabled']:
                        self.preprocessor.update_settings(self.app_state['settings'])
                        source.set_preprocessor(self.preprocessor, enabled=True)

                    # 为图片分配一个检测线程
                    thread_id = f"thread_img_{len(self.detection_thread_manager.get_all_threads()) + 1}"
                    detector = self.get_current_detector()
                    # 检查检测器是否加载成功
                    if not detector:
                        self.logger.error("无法获取当前检测器，图片检测无法进行。")
                        self.stream_manager.remove_source(source_id)
                        return {'error': '模型加载失败，无法处理图片'}, 500

                    self.detection_thread_manager.create_detection_thread(
                        thread_id=thread_id, stream_manager=self.stream_manager, detector=detector,
                        preprocessor=self.preprocessor if self.app_state['preprocessing_enabled'] else None,
                        alert_manager=self.alert_manager, app_state=self.app_state,
                        detection_interval=1,  # 图片可以设置一个较短的间隔，因为它只检测一次
                        results_dir=self.DETECTION_RESULTS)
                    thread = self.detection_thread_manager.get_detection_thread(thread_id)

                    if thread:
                        thread.add_source(source_id)
                    self.app_state['active_sources'][source_id] = {
                        'type': 'image', 'name': name, 'path': file_path, 'thread_id': thread_id
                    }
                    # 图片源通常是临时的，可以选择不保存到 sources.json
                    # self.save_sources() # 如果需要持久化图片源，则取消此行注释
                    self.logger.info(f"图片 {name} 已添加，并分配到检测线程 {thread_id}")
                    return {'success': True, 'source_id': source_id}, 200
                else:
                    return {'error': '无法添加图像文件'}, 500
            else:
                return {'error': '不支持的视频源类型'}, 400

        except Exception as e:
            self.logger.error(f"添加源时出错: {e}", exc_info=True)  # exc_info=True logs the full traceback
            return {'error': f'服务器内部错误: {str(e)}'}, 500

    def remove_source(self, source_id):
        """移除视频源"""
        decoded_source_id = unquote(source_id)
        self.logger.info(f"请求移除数据源: {decoded_source_id}")

        if decoded_source_id in self.app_state['active_sources'] or self.stream_manager.get_source(decoded_source_id):
            # 使用后台线程执行耗时操作，立即返回响应
            threading.Thread(
                target=self.remove_source_internal,
                args=(decoded_source_id,),
                daemon=True
            ).start()
            return {'success': True, 'message': 'Removal process started.'}, 200
        else:
            self.logger.warning(f"尝试移除一个不存在的源: {decoded_source_id}")
            return {'error': f'数据源不存在: {decoded_source_id}'}, 404

    def remove_source_internal(self, source_id):
        """内部移除视频源"""
        try:
            decoded_source_id = unquote(source_id)
            self.logger.info(f"内部开始移除数据源: {decoded_source_id}")

            # 1. 从检测线程中移除
            if decoded_source_id in self.app_state['active_sources']:
                source_info = self.app_state['active_sources'].get(decoded_source_id, {})
                thread_id = source_info.get('thread_id')
                if thread_id:
                    thread = self.detection_thread_manager.get_detection_thread(thread_id)
                    if thread:
                        thread.remove_source(decoded_source_id)
                        self.logger.info(f"已从检测线程 {thread_id} 移除源 {decoded_source_id}")
                        # 如果线程空了，就移除线程
                        if not thread.sources_to_detect:
                            self.detection_thread_manager.remove_detection_thread(thread_id)
                            self.logger.info(f"已移除空检测线程 {thread_id}")

            # 2. 从流管理器中停止并移除源
            source_removed_from_manager = self.stream_manager.remove_source(decoded_source_id)
            if source_removed_from_manager:
                self.logger.info(f"已从 StreamManager 移除源 {decoded_source_id}")
            else:
                self.logger.warning(f"源 {decoded_source_id} 在 StreamManager 中未找到，可能已被移除")

            # 3. 从活动源状态字典中移除
            if decoded_source_id in self.app_state['active_sources']:
                del self.app_state['active_sources'][decoded_source_id]
                self.logger.info(f"已从 app_state['active_sources'] 移除源 {decoded_source_id}")

            # 4. 从帧锁中移除
            if decoded_source_id in self.frame_locks:
                del self.frame_locks[decoded_source_id]
                self.logger.info(f"已从 frame_locks 移除源 {decoded_source_id}")

            # 5. 持久化变更
            self.save_sources()

            self.logger.info(f"数据源 {decoded_source_id} 已被完全移除。")
            return True

        except Exception as e:
            self.logger.error(f"内部移除源 {source_id} 时发生严重错误: {e}", exc_info=True)
            return False


# 全局核心对象
app_core = AppCore()
