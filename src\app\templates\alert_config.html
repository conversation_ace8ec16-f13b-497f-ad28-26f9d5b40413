<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>告警配置 - 边坡检测系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="{{ url_for('main.static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">边坡检测系统</a>
            <div class="navbar-nav">
                <a class="nav-link" href="{{ url_for('main.index') }}">主页</a>
                <a class="nav-link active" href="#">告警配置</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h2>告警配置管理</h2>
                <p class="text-muted">配置邮件告警的冷却策略，解决多源同时检测时的邮件发送问题</p>
            </div>
        </div>

        <!-- 当前状态显示 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>当前告警状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>告警管理器状态</h6>
                                <div id="alertManagerStatus">
                                    <p>加载中...</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>邮件提供者状态</h6>
                                <div id="emailProviderStatus">
                                    <p>加载中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置表单 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>告警管理器配置</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="alertCooldownSeconds" class="form-label">告警冷却时间（秒）</label>
                            <input type="number" class="form-control" id="alertCooldownSeconds" min="0" max="300" value="30">
                            <div class="form-text">同一源的告警冷却时间，0表示无冷却</div>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="updateAlertConfig()">更新告警配置</button>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>邮件冷却策略配置</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="emailCooldownStrategy" class="form-label">冷却策略</label>
                            <select class="form-select" id="emailCooldownStrategy">
                                <option value="none">无冷却限制</option>
                                <option value="global">全局冷却</option>
                                <option value="per_source">按源冷却（推荐）</option>
                            </select>
                            <div class="form-text">
                                <strong>无冷却限制：</strong>每次检测到都发送邮件<br>
                                <strong>全局冷却：</strong>所有源共享冷却时间<br>
                                <strong>按源冷却：</strong>每个源独立冷却（推荐）
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="emailCooldownSeconds" class="form-label">邮件冷却时间（秒）</label>
                            <input type="number" class="form-control" id="emailCooldownSeconds" min="0" max="300" value="30">
                            <div class="form-text">邮件发送的冷却时间，0表示无冷却</div>
                        </div>
                        <button type="button" class="btn btn-success" onclick="updateEmailConfig()">更新邮件配置</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 方案说明 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>三种解决方案说明</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="text-primary">方案1：移除全局冷却</h6>
                                <p>设置邮件冷却策略为"按源冷却"，每个源独立计算冷却时间。四个源同时检测到时，每个源都能发送邮件。</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="applyStrategy('per_source', 30)">应用方案1</button>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-success">方案2：缩短冷却时间</h6>
                                <p>将冷却时间设置为较短的时间（如5-10秒），减少邮件被阻止的概率。</p>
                                <button class="btn btn-outline-success btn-sm" onclick="applyStrategy('per_source', 5)">应用方案2</button>
                            </div>
                            <div class="col-md-4">
                                <h6 class="text-warning">方案3：无冷却限制</h6>
                                <p>完全移除邮件冷却限制，每次检测到异常都发送邮件。注意：可能产生大量邮件。</p>
                                <button class="btn btn-outline-warning btn-sm" onclick="applyStrategy('none', 0)">应用方案3</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">系统通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastBody">
                消息内容
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 显示Toast通知
        function showToast(message, type = 'info') {
            const toastBody = document.getElementById('toastBody');
            const toast = document.getElementById('toast');
            const toastElement = new bootstrap.Toast(toast);
            
            toastBody.textContent = message;
            
            // 根据类型设置样式
            toast.className = 'toast';
            if (type === 'success') {
                toast.classList.add('bg-success', 'text-white');
            } else if (type === 'error') {
                toast.classList.add('bg-danger', 'text-white');
            } else if (type === 'warning') {
                toast.classList.add('bg-warning');
            }
            
            toastElement.show();
        }

        // 加载告警状态
        function loadAlertStatus() {
            $.get('/api/alert_config')
                .done(function(response) {
                    if (response.success) {
                        updateStatusDisplay(response);
                        updateFormValues(response);
                    } else {
                        showToast('加载告警状态失败', 'error');
                    }
                })
                .fail(function() {
                    showToast('加载告警状态失败', 'error');
                });
        }

        // 更新状态显示
        function updateStatusDisplay(data) {
            const alertManager = data.alert_manager;
            const emailProvider = data.email_provider;
            
            let alertManagerHtml = `
                <p><strong>冷却时间：</strong>${alertManager.cooldown_seconds}秒</p>
                <p><strong>队列大小：</strong>${alertManager.queue_size}</p>
                <p><strong>运行状态：</strong>${alertManager.is_running ? '运行中' : '已停止'}</p>
                <p><strong>提供者：</strong>${alertManager.providers.join(', ')}</p>
            `;
            
            let emailProviderHtml = '';
            if (emailProvider && emailProvider.strategy) {
                emailProviderHtml = `
                    <p><strong>冷却策略：</strong>${emailProvider.strategy}</p>
                    <p><strong>冷却时间：</strong>${emailProvider.cooldown_seconds}秒</p>
                `;
                
                if (emailProvider.strategy === 'global' && emailProvider.global_remaining > 0) {
                    emailProviderHtml += `<p><strong>全局剩余冷却：</strong>${emailProvider.global_remaining.toFixed(1)}秒</p>`;
                }
                
                if (emailProvider.strategy === 'per_source' && Object.keys(emailProvider.source_remaining).length > 0) {
                    emailProviderHtml += '<p><strong>各源剩余冷却：</strong></p><ul>';
                    for (const [sourceId, remaining] of Object.entries(emailProvider.source_remaining)) {
                        emailProviderHtml += `<li>${sourceId}: ${remaining.toFixed(1)}秒</li>`;
                    }
                    emailProviderHtml += '</ul>';
                }
            } else {
                emailProviderHtml = '<p>邮件提供者未配置</p>';
            }
            
            document.getElementById('alertManagerStatus').innerHTML = alertManagerHtml;
            document.getElementById('emailProviderStatus').innerHTML = emailProviderHtml;
        }

        // 更新表单值
        function updateFormValues(data) {
            const alertSettings = data.current_settings.alertSettings || {};
            const emailSettings = data.current_settings.emailSettings || {};
            
            document.getElementById('alertCooldownSeconds').value = alertSettings.cooldownSeconds || 30;
            document.getElementById('emailCooldownStrategy').value = emailSettings.cooldownStrategy || 'per_source';
            document.getElementById('emailCooldownSeconds').value = emailSettings.cooldownSeconds || 30;
        }

        // 更新告警配置
        function updateAlertConfig() {
            const cooldownSeconds = parseInt(document.getElementById('alertCooldownSeconds').value);
            
            $.ajax({
                url: '/api/alert_config',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    alert_cooldown_seconds: cooldownSeconds
                }),
                success: function(response) {
                    if (response.success) {
                        showToast('告警配置已更新', 'success');
                        loadAlertStatus();
                    } else {
                        showToast(response.error || '更新失败', 'error');
                    }
                },
                error: function(xhr) {
                    showToast('更新告警配置失败', 'error');
                }
            });
        }

        // 更新邮件配置
        function updateEmailConfig() {
            const strategy = document.getElementById('emailCooldownStrategy').value;
            const cooldownSeconds = parseInt(document.getElementById('emailCooldownSeconds').value);
            
            $.ajax({
                url: '/api/alert_config',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    email_cooldown_strategy: strategy,
                    email_cooldown_seconds: cooldownSeconds
                }),
                success: function(response) {
                    if (response.success) {
                        showToast('邮件配置已更新', 'success');
                        loadAlertStatus();
                    } else {
                        showToast(response.error || '更新失败', 'error');
                    }
                },
                error: function(xhr) {
                    showToast('更新邮件配置失败', 'error');
                }
            });
        }

        // 应用预设策略
        function applyStrategy(strategy, cooldownSeconds) {
            document.getElementById('emailCooldownStrategy').value = strategy;
            document.getElementById('emailCooldownSeconds').value = cooldownSeconds;
            updateEmailConfig();
        }

        // 页面加载时初始化
        $(document).ready(function() {
            loadAlertStatus();
            
            // 每10秒刷新一次状态
            setInterval(loadAlertStatus, 10000);
        });
    </script>
</body>
</html>
