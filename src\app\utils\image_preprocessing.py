import cv2
import numpy as np
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class WeatherCondition(Enum):
    NORMAL = 0
    NIGHT = 1
    RAIN = 2
    FOG = 3

class ImagePreprocessor:
    """
    图像预处理类，用于处理不同天气条件下的图像
    支持夜间、雨天和大雾等条件下的图像增强
    """
    def __init__(self):
        self.condition_handlers = {
            WeatherCondition.NORMAL: self._process_normal,
            WeatherCondition.NIGHT: self._process_night,
            WeatherCondition.RAIN: self._process_rain,
            WeatherCondition.FOG: self._process_fog
        }
        # 定义各种条件的严重程度阈值
        self.severity_thresholds = {
            WeatherCondition.NIGHT: 80,    # 提高夜间检测阈值，使更多场景触发夜间增强
            WeatherCondition.RAIN: 10.5,   # 高频分量高于此值被认为是需要雨天处理
            WeatherCondition.FOG: 90       # 清晰度低于此值被认为是需要去雾
        }
        
    def preprocess(self, image, condition=None):
        """
        根据检测到的天气条件预处理图像
        Args:
            image: 输入图像
            condition: 天气条件，如果为None则自动检测
        Returns:
            预处理后的图像
        """
        if image is None:
            logger.error("输入图像为空")
            return None
            
        if condition is None:
            condition, severity, metrics = self.detect_condition(image, return_severity=True)
            # 只有当条件足够严重才应用处理
            if condition != WeatherCondition.NORMAL and severity < self.severity_thresholds[condition]:
                logger.debug(f"检测到{condition}条件，但严重程度不足（{severity}），不应用处理")
                condition = WeatherCondition.NORMAL
            else:
                logger.debug(f"检测到{condition}条件，严重程度（{severity}），应用处理")
            
        return self.condition_handlers[condition](image)
    
    def detect_condition(self, image, return_severity=False):
        """
        自动检测图像的天气条件
        Args:
            image: 输入图像
            return_severity: 是否返回严重程度和度量值
        Returns:
            如果 return_severity=False，返回检测到的天气条件
            如果 return_severity=True，返回元组 (天气条件, 严重程度值, 度量值字典)
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) > 2 else image
        # 计算平均亮度
        avg_brightness = np.mean(gray)
        # 计算对比度 (标准差)
        contrast = np.std(gray)
        # 计算图像的FFT用于检测雨水和雾气
        f_transform = np.fft.fft2(gray)
        f_shift = np.fft.fftshift(f_transform)
        magnitude_spectrum = 20 * np.log(np.abs(f_shift) + 1)
        # 检测高频分量 (雨水通常会有更多高频分量)
        high_freq_content = np.mean(magnitude_spectrum[magnitude_spectrum.shape[0]//4:3*magnitude_spectrum.shape[0]//4, 
                                                      magnitude_spectrum.shape[1]//4:3*magnitude_spectrum.shape[1]//4])
        # 检测图像清晰度
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        # 收集所有度量值
        metrics = {
            'brightness': avg_brightness,
            'contrast': contrast,
            'high_freq': high_freq_content,
            'clarity': laplacian_var
        }
        
        logger.debug(f"亮度: {avg_brightness}, 对比度: {contrast}, 高频: {high_freq_content}, 清晰度: {laplacian_var}")
        # 计算严重程度和确定天气条件
        condition = WeatherCondition.NORMAL
        severity = 0
        # 夜间判断：降低亮度阈值，增加对比度判断
        if avg_brightness < 100:  # 提高亮度阈值
            condition = WeatherCondition.NIGHT
            # 亮度越低，夜间的情况越严重 (0-100 映射到 100-0)
            severity = max(0, min(100, 100 - avg_brightness))
            # 如果对比度也很低，增加严重程度
            if contrast < 50:
                severity = min(100, severity + 20)
        # 雨天判断：高频分量高且对比度高
        elif high_freq_content > 9.5 and contrast > 50:
            condition = WeatherCondition.RAIN
            # 高频成分越高，雨势越大
            severity = max(0, min(100, (high_freq_content - 9.5) * 10))
        # 大雾判断：清晰度低且亮度高
        elif laplacian_var < 100 and avg_brightness > 100:
            condition = WeatherCondition.FOG
            # 清晰度越低，雾越浓
            severity = max(0, min(100, 100 - laplacian_var))
            
        if return_severity:
            return condition, severity, metrics
        else:
            return condition
    
    def _process_normal(self, image):
        """处理正常天气条件下的图像"""
        return image
    
    def _process_night(self, image):
        """使用改进的Retinex算法增强低照度图像"""
        try:
            # 计算图像特征
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            mean_brightness = np.mean(gray)
            # 转换为浮点型
            img_float = image.astype(np.float64) + 1.0
            # 自适应参数设置
            if mean_brightness < 30:  # 非常暗
                sigma_list = [8, 40, 120]  # 进一步减小尺度范围以增强细节
                alpha = 95   # 略微降低颜色恢复强度
                beta = 32    # 略微降低颜色恢复强度
                G = 145      # 略微降低整体增益
                b = -18      # 略微降低暗部提升
                low_clip = 0.025  # 略微提高低阈值
                high_clip = 0.975  # 略微降低高阈值
            elif mean_brightness < 60:  # 较暗
                sigma_list = [12, 60, 180]
                alpha = 90
                beta = 30
                G = 135
                b = -16
                low_clip = 0.025
                high_clip = 0.975
            else:  # 一般暗度
                sigma_list = [15, 75, 220]
                alpha = 85
                beta = 28
                G = 125
                b = -14
                low_clip = 0.025
                high_clip = 0.975
            
            # 1. 多尺度Retinex处理
            img_retinex = self._multi_scale_retinex(img_float, sigma_list)
            # 2. 颜色恢复
            img_color = self._color_restoration(img_float, alpha, beta)
            # 3. 图像融合
            img_msrcr = G * (img_retinex * img_color + b)
            # 4. 归一化处理
            for i in range(img_msrcr.shape[2]):
                img_msrcr[:, :, i] = (img_msrcr[:, :, i] - np.min(img_msrcr[:, :, i])) / \
                                   (np.max(img_msrcr[:, :, i]) - np.min(img_msrcr[:, :, i])) * 255
            # 5. 限制范围
            img_msrcr = np.uint8(np.minimum(np.maximum(img_msrcr, 0), 255))
            # 6. 颜色均衡
            img_msrcr = self._simplest_color_balance(img_msrcr, low_clip, high_clip)
            # 7. 后处理优化
            # 降噪，使用更精细的降噪参数
            enhanced = cv2.fastNlMeansDenoisingColored(img_msrcr, None, 1, 1, 5, 15)
            # 细节增强
            lab = cv2.cvtColor(enhanced, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            # 使用CLAHE增强对比度，使用更精细的参数
            clahe = cv2.createCLAHE(clipLimit=1.8, tileGridSize=(6, 6))
            l = clahe.apply(l)
            # 调整饱和度，略微降低饱和度
            a = cv2.convertScaleAbs(a, alpha=1.03, beta=0)
            b = cv2.convertScaleAbs(b, alpha=1.03, beta=0)
            # 合并通道
            merged = cv2.merge((l, a, b))
            result = cv2.cvtColor(merged, cv2.COLOR_LAB2BGR)
            # 最终亮度调整，保持亮度不变
            if mean_brightness < 30:
                result = cv2.convertScaleAbs(result, alpha=1.2, beta=20)
            elif mean_brightness < 60:
                result = cv2.convertScaleAbs(result, alpha=1.15, beta=15)
            else:
                result = cv2.convertScaleAbs(result, alpha=1.1, beta=10)
            # 使用改进的USM锐化，更自然的边缘增强
            gaussian = cv2.GaussianBlur(result, (0, 0), 1.5)  # 使用更小的高斯核
            unsharp_mask = cv2.addWeighted(result, 1.6, gaussian, -0.6, 0)  # 降低对比度
            # 根据亮度自适应调整锐化强度
            if mean_brightness < 30:
                result = cv2.addWeighted(result, 0.65, unsharp_mask, 0.35, 0)  # 略微降低锐化强度
            elif mean_brightness < 60:
                result = cv2.addWeighted(result, 0.75, unsharp_mask, 0.25, 0)  # 略微降低锐化强度
            else:
                result = cv2.addWeighted(result, 0.85, unsharp_mask, 0.15, 0)  # 保持锐化强度
            
            return result
            
        except Exception as e:
            logger.error(f"Retinex增强处理失败: {e}")
            return image
    
    def _process_rain(self, image):
        """处理雨天图像"""
        # 降噪
        denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
        # 增强对比度
        lab = cv2.cvtColor(denoised, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        cl = clahe.apply(l)
        merged = cv2.merge((cl, a, b))
        enhanced = cv2.cvtColor(merged, cv2.COLOR_LAB2BGR)
        
        return enhanced
    
    def _process_fog(self, image):
        """使用改进的暗通道先验算法处理大雾图像"""
        try:
            # 归一化图像
            normalized = image.astype('float64') / 255
            # 自适应窗口大小
            h, w = image.shape[:2]
            window_size = max(3, min(15, int(min(h, w) / 100)))
            # 获取暗通道
            dark_channel = self._get_dark_channel(normalized, window_size)
            # 改进的大气光估计
            atmospheric = self._estimate_atmospheric_light(normalized, dark_channel, image)
            # 改进的透射率估计
            transmission = self._estimate_transmission(normalized, atmospheric, window_size)
            # 细化透射率
            refined_transmission = self._refine_transmission(transmission, image)
            # 恢复无雾图像
            result = self._recover_image(normalized, refined_transmission, atmospheric, 0.1)
            # 后处理优化
            result = self._post_process(result)
            # 转换回uint8类型
            result = np.clip(result * 255, 0, 255).astype('uint8')
            
            return result
        except Exception as e:
            logger.error(f"暗通道去雾处理失败: {e}")
            return image
    
    def _get_dark_channel(self, image, window_size):
        """计算暗通道"""
        min_channel = np.min(image, axis=2)
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (window_size, window_size))
        dark_channel = cv2.erode(min_channel, kernel)
        return dark_channel
    
    def _estimate_atmospheric_light(self, image, dark_channel, original_image):
        """改进的大气光估计"""
        h, w = dark_channel.shape
        flat_dark = dark_channel.flatten()
        flat_image = image.reshape(h * w, 3)
        
        # 计算亮度
        gray = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)
        brightness = gray.astype('float64') / 255
        # 取暗通道值最大的前0.1%的像素
        top_idx = (-flat_dark).argsort()[:int(h * w * 0.001)]
        candidate_pixels = flat_image.take(top_idx, axis=0)
        # 计算候选像素的亮度
        candidate_brightness = brightness.flatten()[top_idx]
        # 选择亮度最高的像素
        brightest_idx = np.argmax(candidate_brightness)
        atmospheric = candidate_pixels[brightest_idx]
        # 提高大气光值范围
        atmospheric = np.clip(atmospheric * 1.2, 0.75, 1.0)
        
        return atmospheric
    
    def _estimate_transmission(self, image, atmospheric, window_size):
        """改进的透射率估计"""
        normalized = image / atmospheric
        
        # 计算暗通道
        dark_channel = self._get_dark_channel(normalized, window_size)
        # 进一步降低omega值，使透射率更高
        omega = 0.65
        if np.mean(dark_channel) > 0.8:  # 浓雾
            omega = 0.55
        elif np.mean(dark_channel) < 0.5:  # 薄雾
            omega = 0.65
        
        transmission = 1 - omega * dark_channel
        return transmission
    
    def _refine_transmission(self, transmission, image):
        """改进的透射率细化"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray = gray.astype('float64') / 255
        transmission = transmission.astype('float64')
        
        # 自适应导向滤波参数
        h, w = image.shape[:2]
        radius = max(30, min(60, int(min(h, w) / 20)))
        eps = 0.0001
        # 计算导向滤波的均值
        mean_gray = cv2.boxFilter(gray, cv2.CV_64F, (radius, radius))
        mean_transmission = cv2.boxFilter(transmission, cv2.CV_64F, (radius, radius))
        mean_gray_transmission = cv2.boxFilter(gray * transmission, cv2.CV_64F, (radius, radius))
        var_gray = cv2.boxFilter(gray * gray, cv2.CV_64F, (radius, radius)) - mean_gray * mean_gray
        # 计算导向滤波系数
        a = (mean_gray_transmission - mean_gray * mean_transmission) / (var_gray + eps)
        b = mean_transmission - a * mean_gray
        # 计算细化后的透射率
        refined = cv2.boxFilter(a, cv2.CV_64F, (radius, radius)) * gray + \
                 cv2.boxFilter(b, cv2.CV_64F, (radius, radius))
        # 提高最小透射率下限
        t0 = max(0.4, min(0.6, np.mean(refined) * 0.8))
        return np.clip(refined, t0, 1.0)
    
    def _recover_image(self, image, transmission, atmospheric, t0):
        """恢复无雾图像"""
        transmission = np.maximum(transmission, t0)
        transmission = np.expand_dims(transmission, axis=2)
        result = ((image - atmospheric) / transmission) + atmospheric
        return np.clip(result, 0, 1)
    
    def _post_process(self, image):
        """后处理优化"""
        # 颜色校正
        lab = cv2.cvtColor((image * 255).astype('uint8'), cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        # 显著提高亮度
        l = cv2.add(l, 20)  # 增加更多亮度
        # 降低饱和度
        a = cv2.addWeighted(a, 0.9, np.zeros_like(a), 0.1, 0)
        b = cv2.addWeighted(b, 0.9, np.zeros_like(b), 0.1, 0)
        # 增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        # 合并通道
        merged = cv2.merge((l, a, b))
        result = cv2.cvtColor(merged, cv2.COLOR_LAB2BGR).astype('float64') / 255
        # 细节增强，降低锐化强度
        kernel = np.array([[-0.2, -0.2, -0.2],
                          [-0.2,  2.6, -0.2],
                          [-0.2, -0.2, -0.2]]) / 2.6
        result = cv2.filter2D(result, -1, kernel)
        # 最终颜色调整，显著提高整体亮度
        result = cv2.addWeighted(result, 0.9, np.ones_like(result) * 0.2, 0.2, 0)
        
        return np.clip(result, 0, 1)

    def _single_scale_retinex(self, img, sigma):
        """单尺度Retinex函数"""
        retinex = np.log10(img) - np.log10(cv2.GaussianBlur(img, (0, 0), sigma))
        return retinex

    def _multi_scale_retinex(self, img, sigma_list):
        """多尺度Retinex函数"""
        retinex = np.zeros_like(img)
        for sigma in sigma_list:
            retinex += self._single_scale_retinex(img, sigma)
        retinex = retinex / len(sigma_list)
        return retinex

    def _color_restoration(self, img, alpha, beta):
        """颜色恢复函数"""
        img_sum = np.sum(img, axis=2, keepdims=True)
        color_restoration = beta * (np.log10(alpha * img) - np.log10(img_sum))
        return color_restoration

    def _simplest_color_balance(self, img, low_clip, high_clip):
        """最简单的颜色均衡函数"""
        total = img.shape[0] * img.shape[1]
        for i in range(img.shape[2]):
            unique, counts = np.unique(img[:, :, i], return_counts=True)
            current = 0
            for u, c in zip(unique, counts):
                if float(current) / total < low_clip:
                    low_val = u
                if float(current) / total < high_clip:
                    high_val = u
                current += c
            img[:, :, i] = np.maximum(np.minimum(img[:, :, i], high_val), low_val)
        return img


# 实现可插拔的预处理器接口
class CustomImagePreprocessor:
    """
    自定义图像预处理接口类
    允许新算法的接入和替换
    """
    def __init__(self):
        self.preprocessors = {
            "default": ImagePreprocessor(),
            # 其他预处理器可以在这里注册
        }
        self.active_preprocessor = "default"
        # 环境设置
        self.settings = {
            'nightMode': False,  # 夜间增强
            'rainMode': False,   # 雨天增强
            'fogMode': False     # 大雾增强
        }
    
    def update_settings(self, settings):
        """
        更新环境设置
        Args:
            settings: 包含环境设置的字典
        """
        if 'nightMode' in settings:
            self.settings['nightMode'] = settings['nightMode']
        if 'rainMode' in settings:
            self.settings['rainMode'] = settings['rainMode']
        if 'fogMode' in settings:
            self.settings['fogMode'] = settings['fogMode']

        # 将日志级别从debug降低到trace，避免过多输出
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(f"预处理器环境设置已更新: {self.settings}")
    
    def register_preprocessor(self, name, preprocessor):
        """
        注册新的预处理器
        Args:
            name: 预处理器名称
            preprocessor: 预处理器对象，需要实现preprocess方法
        """
        self.preprocessors[name] = preprocessor
        logger.info(f"注册新预处理器: {name}")
    
    def set_active_preprocessor(self, name):
        """
        设置活动预处理器
        Args:
            name: 预处理器名称
        """
        if name in self.preprocessors:
            self.active_preprocessor = name
            logger.info(f"设置活动预处理器: {name}")
        else:
            logger.error(f"预处理器 {name} 不存在")
    
    def get_active_preprocessor(self):
        """
        获取当前活动预处理器
        Returns:
            当前活动预处理器名称
        """
        return self.active_preprocessor
    
    def get_all_preprocessors(self):
        """
        获取所有可用的预处理器
        Returns:
            所有预处理器名称列表
        """
        return list(self.preprocessors.keys())
    
    def preprocess(self, image, condition=None):
        """
        使用当前活动预处理器处理图像，同时尊重用户的环境设置
        Args:
            image: 输入图像
            condition: 天气条件，如果为None则自动检测
        Returns:
            预处理后的图像
        """
        # 如果提供了condition，使用它；否则，自动检测
        if condition is None:
            # 获取条件和严重程度
            condition, severity, metrics = self.preprocessors[self.active_preprocessor].detect_condition(image, return_severity=True)
            # 根据用户设置调整condition
            if condition == WeatherCondition.NIGHT and not self.settings['nightMode']:
                logger.debug("夜间增强已禁用，使用正常处理")
                condition = WeatherCondition.NORMAL

            elif condition == WeatherCondition.RAIN and not self.settings['rainMode']:
                logger.debug("雨天增强已禁用，使用正常处理")
                condition = WeatherCondition.NORMAL
            
            elif condition == WeatherCondition.FOG and not self.settings['fogMode']:
                logger.debug("大雾增强已禁用，使用正常处理")
                condition = WeatherCondition.NORMAL

            # 仅当需要处理时才传递condition，否则传递None让preprocessor自己判断
            if condition == WeatherCondition.NORMAL:
                return image  # 如果判断为正常条件，直接返回原图像，不进行任何处理
            
        # 使用调整后的condition处理图像
        return self.preprocessors[self.active_preprocessor].preprocess(image, condition) 